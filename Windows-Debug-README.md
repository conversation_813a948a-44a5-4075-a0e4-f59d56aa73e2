# Windows 调试启动器使用说明

## 概述

当MEEA-VIOFO在Windows上显示空白页面时，可以使用这些调试启动器来启用开发者工具进行问题排查。

## 文件说明

### MEEA-VIOFO-DEBUG.bat
- Windows批处理文件
- 双击即可运行
- 兼容所有Windows版本

### MEEA-VIOFO-DEBUG.ps1
- PowerShell脚本
- 提供更好的用户界面
- 需要PowerShell支持

## 使用步骤

1. **复制文件**
   - 将调试启动器复制到MEEA-VIOFO.exe所在目录
   - 通常位于: `C:\Program Files\MEEA-VIOFO\` 或 `C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\`

2. **运行调试启动器**
   - 双击 `MEEA-VIOFO-DEBUG.bat` 或 `MEEA-VIOFO-DEBUG.ps1`
   - 按照提示启动应用程序

3. **调试空白页面**
   - 如果页面显示空白，按 `Ctrl+Shift+I` 打开开发者工具
   - 查看 Console 标签页中的错误信息
   - 查看 Network 标签页检查资源加载情况

## 调试功能

启用调试模式后，应用程序将具有以下功能：

- ✅ **开发者工具**: `Ctrl+Shift+I`
- ✅ **页面刷新**: `Ctrl+R` 或 `F5`
- ✅ **详细日志**: 控制台输出详细的加载信息
- ✅ **错误捕获**: 自动捕获JavaScript错误和Promise拒绝
- ✅ **加载状态检查**: 自动检查页面和React组件加载状态

## 常见问题排查

### 1. 页面完全空白
- 打开开发者工具查看Console错误
- 检查Network标签页是否有资源加载失败
- 查看是否有JavaScript错误

### 2. 页面部分加载
- 检查React组件是否正确挂载
- 查看是否有CSS样式问题
- 检查API调用是否成功

### 3. 应用程序无法启动
- 检查MEEA-VIOFO.exe是否存在
- 确认Windows版本兼容性
- 检查是否有防病毒软件阻止

## 环境变量

调试启动器会设置以下环境变量：

- `MEEA_DEBUG=1`: 启用应用程序调试模式
- `ELECTRON_ENABLE_LOGGING=1`: 启用Electron详细日志

## 注意事项

- 调试模式仅用于问题排查，不建议日常使用
- 开发者工具可能会影响应用程序性能
- 调试信息可能包含敏感数据，请谨慎分享

## 技术支持

如果问题仍然存在，请：

1. 使用调试启动器收集错误信息
2. 截图开发者工具中的错误信息
3. 联系技术支持并提供详细的错误日志
