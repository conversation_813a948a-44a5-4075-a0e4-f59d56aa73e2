{"name": "meea-viofo-all", "version": "25.07.18-1805", "description": "MEEA VIOFO - 专业的行车记录仪视频管理工具", "main": "electron/main.js", "homepage": "./", "author": "PerccyKing <<EMAIL>>", "license": "MIT", "engines": {"node": ">=20.19.0"}, "scripts": {"dev": "concurrently \"yarn dev:vite\" \"wait-on http://localhost:5174 && yarn dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "prebuild": "node scripts/generate-build-version.js && node scripts/inject-version.js", "build": "yarn prebuild && vite build", "preview": "vite preview", "electron": "electron .", "pack": "electron-builder --dir", "predist": "node scripts/verify-platform-resources.js && node scripts/verify-ffmpeg-binaries.js && node scripts/generate-build-version.js && node scripts/inject-version.js", "dist": "yarn predist && yarn build && electron-builder --mac --win --linux --publish=never", "dist:win:x64": "yarn predist && yarn build && electron-builder --win --x64 --publish=never", "dist:win:arm64": "yarn predist && yarn build && electron-builder --win --arm64 --publish=never", "dist:win": "yarn dist:win:x64 && yarn dist:win:arm64", "dist:mac:x64": "yarn predist && yarn build && electron-builder --mac --x64 --publish=never", "dist:mac:arm64": "yarn predist && yarn build && electron-builder --mac --arm64 --publish=never", "dist:mac": "yarn dist:mac:x64 && yarn dist:mac:arm64", "dist:linux:x64": "yarn predist && yarn build && electron-builder --linux --x64 --publish=never", "dist:linux:arm64": "yarn predist && yarn build && electron-builder --linux --arm64 --publish=never", "dist:linux": "yarn dist:linux:x64 && yarn dist:linux:arm64", "dist:debug:win:x64": "yarn predist && yarn build && electron-builder --win --x64 --publish=never --config.productName=\"MEEA-VIOFO-DEBUG\" --config.appId=\"com.meea.viofo.debug\"", "dist:debug:win:arm64": "yarn predist && yarn build && electron-builder --win --arm64 --publish=never --config.productName=\"MEEA-VIOFO-DEBUG\" --config.appId=\"com.meea.viofo.debug\"", "dist:debug:mac:x64": "yarn predist && yarn build && electron-builder --mac --x64 --publish=never --config.productName=\"MEEA-VIOFO-DEBUG\" --config.appId=\"com.meea.viofo.debug\"", "dist:debug:mac:arm64": "yarn predist && yarn build && electron-builder --mac --arm64 --publish=never --config.productName=\"MEEA-VIOFO-DEBUG\" --config.appId=\"com.meea.viofo.debug\"", "dist:all": "yarn dist:win && yarn dist:mac && yarn dist:linux", "cleanup": "./scripts/cleanup-space.sh", "setup:ffmpeg": "node scripts/setup-ffmpeg.js", "verify:ffmpeg": "node scripts/verify-ffmpeg-binaries.js", "verify:platform-resources": "node scripts/verify-platform-resources.js", "test:build-config": "node scripts/test-build-config.js --quick", "test:build-config:full": "node scripts/test-build-config.js", "setup:ffmpeg:current": "node scripts/setup-ffmpeg.js --copy-current", "test:ffmpeg:fix": "node scripts/test-ffmpeg-fix.js", "download:ffmpeg:windows": "node scripts/download-windows-ffmpeg.js", "download:ffmpeg:windows:simple": "./scripts/download-ffmpeg-simple.sh", "download:ffmpeg:windows:x64": "node scripts/download-windows-ffmpeg.js --x64", "enable:debug": "node scripts/enable-debug-mode.js", "disable:debug": "node scripts/disable-debug-mode.js", "check:debug": "node scripts/enable-debug-mode.js --status", "test:debug": "node scripts/test-debug-features.js", "force:debug": "node scripts/force-enable-debug.js", "test:arch-build": "node scripts/test-arch-specific-build.js"}, "build": {"appId": "com.meea.viofo", "productName": "MEEA-VIOFO", "copyright": "Copyright © 2024 MEEA", "artifactName": "${productName}-${version}-${os}-${arch}.${ext}", "directories": {"output": "dist", "buildResources": "build"}, "files": ["dist/assets/**/*", "dist/index.html", "dist/*.svg", "dist/*.png", "electron/**/*", "keys/**/*", "package.json", "node_modules/fluent-ffmpeg/**/*", "node_modules/exiftool-vendored/**/*", "!node_modules/exiftool-vendored/bin/**/*", "!dist/mac/**/*", "!dist/mac-arm64/**/*", "!dist/linux/**/*", "!dist/win-*/**/*", "!dist/*.dmg", "!dist/*.zip", "!dist/*.exe", "!dist/*.AppImage", "!dist/*.tar.gz", "!dist/*.deb", "!dist/*.rpm", "!dist/builder-*.yml", "!dist/builder-*.yaml"], "extraResources": [], "mac": {"icon": "build/icons/icon.icns", "category": "public.app-category.video", "hardenedRuntime": false, "gatekeeperAssess": false, "identity": null, "type": "distribution", "minimumSystemVersion": "10.15.0", "target": [{"target": "dmg"}, {"target": "zip"}], "extraResources": [{"from": "ffmpeg/mac-${arch}", "to": "ffmpeg/mac-${arch}", "filter": ["**/*"]}], "extendInfo": {"NSCameraUsageDescription": "此应用需要访问摄像头以处理视频文件", "NSMicrophoneUsageDescription": "此应用需要访问麦克风以处理音频文件", "NSLocationUsageDescription": "此应用需要访问位置信息以显示GPS轨迹"}, "bundleVersion": "25.07.18-1805"}, "dmg": {"sign": false, "writeUpdateInfo": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"icon": "build/icons/icon.ico", "target": [{"target": "nsis"}, {"target": "zip"}], "extraResources": [{"from": "ffmpeg/win-${arch}", "to": "ffmpeg/win-${arch}", "filter": ["**/*"]}, {"from": "exiftool/win-${arch}", "to": "exiftool/win-${arch}", "filter": ["**/*"]}], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-Setup-${version}-${os}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MEEA-VIOFO", "artifactName": "${productName}-Setup-${version}-${os}-${arch}.${ext}", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Video", "include": "build/installer.nsh"}, "linux": {"icon": "build/icons/icon.png", "category": "AudioVideo", "synopsis": "专业的行车记录仪视频管理工具", "description": "MEEA VIOFO 是一款专业的行车记录仪视频管理工具，支持GPS轨迹显示、视频预览、缩略图生成等功能。", "desktop": {"entry": {"Name": "MEEA-VIOFO", "Comment": "专业的行车记录仪视频管理工具", "Keywords": "video;dashcam;gps;media;player;", "StartupWMClass": "MEEA-VIOFO"}}, "extraResources": [{"from": "ffmpeg/linux-${arch}", "to": "ffmpeg/linux-${arch}", "filter": ["**/*"]}], "target": [{"target": "AppImage"}, {"target": "tar.gz"}, {"target": "deb"}, {"target": "rpm"}]}, "compression": "normal", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "publish": null, "generateUpdatesFilesForAllChannels": false, "detectUpdateChannel": false, "asar": true, "asarUnpack": ["node_modules/exiftool-vendored.pl/bin/**/*"], "afterPack": "scripts/after-pack.js", "buildVersion": "25.07.18-1805"}, "devDependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "electron-builder": "^26.0.12", "typescript": "^5.8.3", "vite": "^7.0.3", "wait-on": "^8.0.3"}, "optionalDependencies": {"dmg-license": "^1.0.11"}, "dependencies": {"@chakra-ui/react": "^3.22.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "exiftool-vendored": "^30.3.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.23.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-is": "^19.1.0", "recharts": "^3.1.0"}}