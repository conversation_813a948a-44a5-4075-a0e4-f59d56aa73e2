#!/usr/bin/env node

/**
 * 验证FFmpeg二进制文件脚本
 * 
 * 此脚本验证所有平台的FFmpeg二进制文件是否存在且可执行
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

const projectRoot = path.join(__dirname, '..');
const ffmpegDir = path.join(projectRoot, 'ffmpeg');

// 平台配置
const platforms = {
  'win-x64': { ffmpeg: 'ffmpeg.exe', ffprobe: 'ffprobe.exe' },
  'win-arm64': { ffmpeg: 'ffmpeg.exe', ffprobe: 'ffprobe.exe' },
  'mac-x64': { ffmpeg: 'ffmpeg', ffprobe: 'ffprobe' },
  'mac-arm64': { ffmpeg: 'ffmpeg', ffprobe: 'ffprobe' },
  'linux-x64': { ffmpeg: 'ffmpeg', ffprobe: 'ffprobe' },
  'linux-arm64': { ffmpeg: 'ffmpeg', ffprobe: 'ffprobe' }
};

/**
 * 检查文件是否存在且可执行
 */
function checkBinary(filePath) {
  if (!fs.existsSync(filePath)) {
    return { exists: false, executable: false, size: 0 };
  }
  
  const stats = fs.statSync(filePath);
  const size = stats.size;
  
  // 在Unix系统上检查执行权限
  let executable = true;
  if (process.platform !== 'win32') {
    executable = !!(stats.mode & parseInt('111', 8));
  }
  
  return { exists: true, executable, size };
}

/**
 * 测试二进制文件是否可以执行
 */
function testBinary(filePath) {
  return new Promise((resolve) => {
    const child = spawn(filePath, ['-version'], {
      stdio: 'pipe',
      timeout: 5000
    });
    
    let output = '';
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    child.on('close', (code) => {
      const works = (code === 0 || output.includes('ffmpeg') || output.includes('ffprobe'));
      resolve({ works, output: output.substring(0, 100) });
    });
    
    child.on('error', () => {
      resolve({ works: false, output: 'Failed to execute' });
    });
  });
}

/**
 * 验证所有平台的FFmpeg二进制文件
 */
async function verifyAllBinaries() {
  console.log('🔍 验证FFmpeg二进制文件...\n');
  
  let allGood = true;
  const results = {};
  
  for (const [platform, binaries] of Object.entries(platforms)) {
    console.log(`📦 ${platform}:`);
    results[platform] = {};
    
    const platformDir = path.join(ffmpegDir, platform);
    
    if (!fs.existsSync(platformDir)) {
      console.log(`  ❌ 目录不存在: ${platformDir}`);
      allGood = false;
      results[platform].error = 'Directory not found';
      continue;
    }
    
    for (const [name, filename] of Object.entries(binaries)) {
      const filePath = path.join(platformDir, filename);
      const check = checkBinary(filePath);
      
      if (!check.exists) {
        console.log(`  ❌ ${name}: 文件不存在`);
        allGood = false;
        results[platform][name] = { status: 'missing' };
      } else if (!check.executable) {
        console.log(`  ⚠️ ${name}: 文件存在但不可执行 (${(check.size / 1024 / 1024).toFixed(1)}MB)`);
        results[platform][name] = { status: 'not_executable', size: check.size };
      } else {
        const sizeMB = (check.size / 1024 / 1024).toFixed(1);
        console.log(`  ✅ ${name}: ${sizeMB}MB`);
        results[platform][name] = { status: 'ok', size: check.size };
        
        // 对于当前平台，尝试执行测试
        if (platform.startsWith(getCurrentPlatform())) {
          try {
            const test = await testBinary(filePath);
            if (test.works) {
              console.log(`    ✅ 执行测试通过`);
            } else {
              console.log(`    ⚠️ 执行测试失败: ${test.output}`);
            }
          } catch (error) {
            console.log(`    ⚠️ 执行测试出错: ${error.message}`);
          }
        }
      }
    }
    console.log('');
  }
  
  return { success: allGood, results };
}

/**
 * 获取当前平台名称
 */
function getCurrentPlatform() {
  const platform = process.platform;
  if (platform === 'darwin') return 'mac';
  if (platform === 'win32') return 'win';
  if (platform === 'linux') return 'linux';
  return platform;
}

/**
 * 生成验证报告
 */
function generateReport(results) {
  console.log('📊 验证报告:');
  
  let totalBinaries = 0;
  let okBinaries = 0;
  let missingBinaries = 0;
  let notExecutableBinaries = 0;
  
  for (const [platform, platformResults] of Object.entries(results.results)) {
    if (platformResults.error) continue;
    
    for (const [name, result] of Object.entries(platformResults)) {
      totalBinaries++;
      if (result.status === 'ok') okBinaries++;
      else if (result.status === 'missing') missingBinaries++;
      else if (result.status === 'not_executable') notExecutableBinaries++;
    }
  }
  
  console.log(`  总计: ${totalBinaries} 个二进制文件`);
  console.log(`  ✅ 正常: ${okBinaries}`);
  console.log(`  ❌ 缺失: ${missingBinaries}`);
  console.log(`  ⚠️ 不可执行: ${notExecutableBinaries}`);
  
  if (results.success) {
    console.log('\n🎉 所有FFmpeg二进制文件验证通过！');
  } else {
    console.log('\n❌ FFmpeg二进制文件验证失败！');
    console.log('\n💡 解决方案:');
    console.log('  1. 运行: yarn setup:ffmpeg');
    console.log('  2. 或手动下载FFmpeg二进制文件到对应目录');
    console.log('  3. 确保文件具有执行权限 (Unix系统)');
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🔍 FFmpeg二进制文件验证脚本

用法:
  node scripts/verify-ffmpeg-binaries.js [选项]

选项:
  --help, -h     显示此帮助信息
  --quiet, -q    静默模式，只显示结果

说明:
  此脚本验证所有平台的FFmpeg二进制文件是否存在且可执行。
  对于当前平台的二进制文件，还会尝试执行测试。
`);
    return;
  }
  
  const quiet = args.includes('--quiet') || args.includes('-q');
  
  try {
    if (!quiet) {
      console.log('🚀 开始验证FFmpeg二进制文件...\n');
    }
    
    const results = await verifyAllBinaries();
    
    if (!quiet) {
      generateReport(results);
    }
    
    process.exit(results.success ? 0 : 1);
  } catch (error) {
    console.error('❌ 验证过程出错:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  verifyAllBinaries,
  checkBinary,
  testBinary
};
