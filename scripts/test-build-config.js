#!/usr/bin/env node

/**
 * 测试构建配置脚本
 * 
 * 此脚本测试不同平台的构建配置是否正确
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

const projectRoot = path.join(__dirname, '..');

/**
 * 执行命令并返回结果
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 执行命令: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'pipe',
      cwd: projectRoot,
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`命令执行失败: ${command} ${args.join(' ')}, 退出码: ${code}, stderr: ${stderr}`));
      }
    });
    
    child.on('error', (error) => {
      reject(new Error(`命令执行错误: ${error.message}`));
    });
  });
}

/**
 * 测试平台特定的构建命令
 */
async function testPlatformBuild(platform, arch) {
  console.log(`\n🧪 测试 ${platform}-${arch} 构建配置...`);
  
  try {
    // 使用 --dir 参数进行测试构建（不生成最终安装包）
    const buildCommand = `dist:${platform}:${arch}`.replace(':dist:', ':');
    const args = ['run', buildCommand.replace('dist:', 'pack:')]; // 使用pack而不是dist进行测试
    
    // 如果没有对应的pack命令，则跳过
    const packageJson = JSON.parse(fs.readFileSync(path.join(projectRoot, 'package.json'), 'utf8'));
    const packCommand = `pack:${platform}:${arch}`;
    
    if (!packageJson.scripts[packCommand]) {
      console.log(`⚠️ 跳过测试: 没有找到 ${packCommand} 命令`);
      return true;
    }
    
    const result = await executeCommand('yarn', args, { timeout: 300000 }); // 5分钟超时
    console.log(`✅ ${platform}-${arch} 构建配置测试通过`);
    return true;
  } catch (error) {
    console.error(`❌ ${platform}-${arch} 构建配置测试失败:`, error.message);
    return false;
  }
}

/**
 * 验证构建产物中的资源
 */
function verifyBuildArtifacts(platform, arch) {
  console.log(`\n🔍 验证 ${platform}-${arch} 构建产物...`);
  
  // 根据平台确定构建输出目录
  let buildDir;
  if (platform === 'mac') {
    buildDir = path.join(projectRoot, 'dist', 'mac');
  } else if (platform === 'win') {
    buildDir = path.join(projectRoot, 'dist', 'win-unpacked');
  } else if (platform === 'linux') {
    buildDir = path.join(projectRoot, 'dist', 'linux-unpacked');
  }
  
  if (!fs.existsSync(buildDir)) {
    console.log(`⚠️ 构建目录不存在: ${buildDir}`);
    return false;
  }
  
  // 检查资源目录
  let resourcesDir;
  if (platform === 'mac') {
    const appDirs = fs.readdirSync(buildDir).filter(name => name.endsWith('.app'));
    if (appDirs.length === 0) {
      console.log(`❌ 未找到.app目录`);
      return false;
    }
    resourcesDir = path.join(buildDir, appDirs[0], 'Contents', 'Resources');
  } else {
    resourcesDir = path.join(buildDir, 'resources');
  }
  
  if (!fs.existsSync(resourcesDir)) {
    console.log(`❌ 资源目录不存在: ${resourcesDir}`);
    return false;
  }
  
  // 检查FFmpeg
  const ffmpegDir = path.join(resourcesDir, 'ffmpeg', `${platform}-${arch}`);
  if (!fs.existsSync(ffmpegDir)) {
    console.log(`❌ FFmpeg目录不存在: ${ffmpegDir}`);
    return false;
  }
  
  const ffmpegExt = platform === 'win' ? '.exe' : '';
  const ffmpegFiles = [`ffmpeg${ffmpegExt}`, `ffprobe${ffmpegExt}`];
  
  for (const fileName of ffmpegFiles) {
    const filePath = path.join(ffmpegDir, fileName);
    if (!fs.existsSync(filePath)) {
      console.log(`❌ FFmpeg文件不存在: ${filePath}`);
      return false;
    }
    console.log(`✅ 找到FFmpeg文件: ${fileName}`);
  }
  
  // 检查Windows的ExifTool
  if (platform === 'win') {
    const exiftoolDir = path.join(resourcesDir, 'exiftool', `${platform}-${arch}`);
    if (!fs.existsSync(exiftoolDir)) {
      console.log(`❌ ExifTool目录不存在: ${exiftoolDir}`);
      return false;
    }
    console.log(`✅ 找到ExifTool目录`);
  }
  
  console.log(`✅ ${platform}-${arch} 构建产物验证通过`);
  return true;
}

/**
 * 测试所有平台配置
 */
async function testAllPlatforms() {
  console.log('🚀 开始测试所有平台构建配置...\n');
  
  const platforms = [
    { platform: 'win', arch: 'x64' },
    { platform: 'win', arch: 'arm64' },
    { platform: 'mac', arch: 'x64' },
    { platform: 'mac', arch: 'arm64' },
    { platform: 'linux', arch: 'x64' },
    { platform: 'linux', arch: 'arm64' }
  ];
  
  let allPassed = true;
  
  for (const { platform, arch } of platforms) {
    try {
      // 注意：实际的构建测试可能需要很长时间，这里我们主要验证配置
      console.log(`\n📦 验证 ${platform}-${arch} 配置...`);
      
      // 验证资源文件存在
      const ffmpegDir = path.join(projectRoot, 'ffmpeg', `${platform}-${arch}`);
      if (!fs.existsSync(ffmpegDir)) {
        console.log(`❌ FFmpeg目录不存在: ${ffmpegDir}`);
        allPassed = false;
        continue;
      }
      
      if (platform === 'win') {
        const exiftoolDir = path.join(projectRoot, 'exiftool', `${platform}-${arch}`);
        if (!fs.existsSync(exiftoolDir)) {
          console.log(`❌ ExifTool目录不存在: ${exiftoolDir}`);
          allPassed = false;
          continue;
        }
      }
      
      console.log(`✅ ${platform}-${arch} 配置验证通过`);
      
    } catch (error) {
      console.error(`❌ ${platform}-${arch} 测试失败:`, error.message);
      allPassed = false;
    }
  }
  
  return allPassed;
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📦 构建配置测试脚本

用法:
  node scripts/test-build-config.js [选项]

选项:
  --help, -h     显示此帮助信息
  --quick        快速测试（仅验证配置，不执行构建）

说明:
  此脚本测试不同平台的构建配置是否正确，包括：
  - 验证资源文件存在
  - 检查package.json配置
  - 测试构建命令（可选）
`);
    return;
  }
  
  const quickTest = args.includes('--quick');
  
  try {
    if (quickTest) {
      console.log('🚀 执行快速配置测试...\n');
      const passed = await testAllPlatforms();
      
      if (passed) {
        console.log('\n🎉 所有平台配置测试通过！');
        process.exit(0);
      } else {
        console.log('\n❌ 部分平台配置测试失败！');
        process.exit(1);
      }
    } else {
      console.log('🚀 执行完整构建测试...\n');
      console.log('⚠️ 注意：完整构建测试需要较长时间，建议先运行 --quick 测试');
      
      // 这里可以添加完整的构建测试逻辑
      const passed = await testAllPlatforms();
      
      if (passed) {
        console.log('\n🎉 构建配置测试通过！');
        process.exit(0);
      } else {
        console.log('\n❌ 构建配置测试失败！');
        process.exit(1);
      }
    }
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  testPlatformBuild,
  verifyBuildArtifacts,
  testAllPlatforms
};
