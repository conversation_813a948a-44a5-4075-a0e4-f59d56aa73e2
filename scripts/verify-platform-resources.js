#!/usr/bin/env node

/**
 * 验证平台特定资源配置脚本
 * 
 * 此脚本验证每个平台的依赖工具是否正确配置和存在
 */

const fs = require('fs');
const path = require('path');

const projectRoot = path.join(__dirname, '..');
const ffmpegDir = path.join(projectRoot, 'ffmpeg');
const exiftoolDir = path.join(projectRoot, 'exiftool');

// 平台和架构配置
const platforms = {
  'win': {
    architectures: ['x64', 'arm64'],
    ffmpegFiles: ['ffmpeg.exe', 'ffprobe.exe'],
    exiftoolRequired: true,
    exiftoolFiles: ['exiftool_files/exiftool.pl', 'exiftool_files/perl.exe']
  },
  'mac': {
    architectures: ['x64', 'arm64'],
    ffmpegFiles: ['ffmpeg', 'ffprobe'],
    exiftoolRequired: false,
    exiftoolFiles: []
  },
  'linux': {
    architectures: ['x64', 'arm64'],
    ffmpegFiles: ['ffmpeg', 'ffprobe'],
    exiftoolRequired: false,
    exiftoolFiles: []
  }
};

/**
 * 验证FFmpeg文件
 */
function verifyFFmpegFiles() {
  console.log('🔍 验证FFmpeg文件...\n');
  
  let allGood = true;
  
  for (const [platform, config] of Object.entries(platforms)) {
    console.log(`📦 ${platform.toUpperCase()} 平台:`);
    
    for (const arch of config.architectures) {
      const platformDir = path.join(ffmpegDir, `${platform}-${arch}`);
      console.log(`  🏗️ ${arch} 架构:`);
      
      if (!fs.existsSync(platformDir)) {
        console.log(`    ❌ 目录不存在: ${platformDir}`);
        allGood = false;
        continue;
      }
      
      for (const fileName of config.ffmpegFiles) {
        const filePath = path.join(platformDir, fileName);
        const exists = fs.existsSync(filePath);
        
        if (exists) {
          const stats = fs.statSync(filePath);
          const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
          console.log(`    ✅ ${fileName} (${sizeMB}MB)`);
        } else {
          console.log(`    ❌ ${fileName} (缺失)`);
          allGood = false;
        }
      }
    }
    console.log('');
  }
  
  return allGood;
}

/**
 * 验证ExifTool文件
 */
function verifyExifToolFiles() {
  console.log('🔍 验证ExifTool文件...\n');
  
  let allGood = true;
  
  for (const [platform, config] of Object.entries(platforms)) {
    if (!config.exiftoolRequired) {
      console.log(`📦 ${platform.toUpperCase()} 平台: 不需要ExifTool`);
      continue;
    }
    
    console.log(`📦 ${platform.toUpperCase()} 平台:`);
    
    for (const arch of config.architectures) {
      const platformDir = path.join(exiftoolDir, `${platform}-${arch}`);
      console.log(`  🏗️ ${arch} 架构:`);
      
      if (!fs.existsSync(platformDir)) {
        console.log(`    ❌ 目录不存在: ${platformDir}`);
        allGood = false;
        continue;
      }
      
      for (const fileName of config.exiftoolFiles) {
        const filePath = path.join(platformDir, fileName);
        const exists = fs.existsSync(filePath);
        
        if (exists) {
          const stats = fs.statSync(filePath);
          const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
          console.log(`    ✅ ${fileName} (${sizeMB}MB)`);
        } else {
          console.log(`    ❌ ${fileName} (缺失)`);
          allGood = false;
        }
      }
    }
    console.log('');
  }
  
  return allGood;
}

/**
 * 验证package.json配置
 */
function verifyPackageJsonConfig() {
  console.log('🔍 验证package.json配置...\n');
  
  const packageJsonPath = path.join(projectRoot, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  let allGood = true;
  
  // 检查平台特定的extraResources配置
  const platformConfigs = ['mac', 'win', 'linux'];
  
  for (const platform of platformConfigs) {
    console.log(`📦 ${platform.toUpperCase()} 配置:`);
    
    const config = packageJson.build[platform];
    if (!config) {
      console.log(`  ❌ 缺少${platform}配置`);
      allGood = false;
      continue;
    }
    
    if (!config.extraResources || !Array.isArray(config.extraResources)) {
      console.log(`  ❌ 缺少extraResources配置`);
      allGood = false;
      continue;
    }
    
    // 检查FFmpeg配置
    const ffmpegResource = config.extraResources.find(r => 
      r.from && r.from.includes('ffmpeg')
    );
    
    if (ffmpegResource) {
      console.log(`  ✅ FFmpeg资源配置: ${ffmpegResource.from}`);
    } else {
      console.log(`  ❌ 缺少FFmpeg资源配置`);
      allGood = false;
    }
    
    // 检查Windows的ExifTool配置
    if (platform === 'win') {
      const exiftoolResource = config.extraResources.find(r => 
        r.from && r.from.includes('exiftool')
      );
      
      if (exiftoolResource) {
        console.log(`  ✅ ExifTool资源配置: ${exiftoolResource.from}`);
      } else {
        console.log(`  ❌ 缺少ExifTool资源配置`);
        allGood = false;
      }
    }
    
    console.log('');
  }
  
  return allGood;
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始验证平台特定资源配置...\n');
  
  const ffmpegOk = verifyFFmpegFiles();
  const exiftoolOk = verifyExifToolFiles();
  const configOk = verifyPackageJsonConfig();
  
  console.log('📊 验证结果:');
  console.log(`  FFmpeg文件: ${ffmpegOk ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  ExifTool文件: ${exiftoolOk ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  配置文件: ${configOk ? '✅ 通过' : '❌ 失败'}`);
  
  if (ffmpegOk && exiftoolOk && configOk) {
    console.log('\n🎉 所有验证通过！平台特定资源配置正确。');
    process.exit(0);
  } else {
    console.log('\n❌ 验证失败！请检查上述问题。');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  verifyFFmpegFiles,
  verifyExifToolFiles,
  verifyPackageJsonConfig
};
