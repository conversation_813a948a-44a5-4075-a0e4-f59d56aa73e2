#!/usr/bin/env node

/**
 * 创建Windows调试启动器
 * 
 * 此脚本创建一个Windows批处理文件，用于启用调试模式
 */

const fs = require('fs');
const path = require('path');

const projectRoot = path.join(__dirname, '..');

/**
 * 创建macOS调试启动器
 */
function createMacOSDebugLauncher() {
  console.log('🚀 创建macOS调试启动器...\n');

  // Shell脚本内容
  const shellContent = `#!/bin/bash

echo "========================================"
echo "MEEA-VIOFO macOS 调试启动器"
echo "========================================"
echo ""
echo "此启动器将启用以下调试功能："
echo "- 开发者工具快捷键 (Cmd+Option+I)"
echo "- 页面刷新快捷键 (Cmd+R)"
echo "- 详细的错误日志"
echo "- 页面加载状态检查"
echo ""
echo "如果应用显示空白页面，请："
echo "1. 按 Cmd+Option+I 打开开发者工具"
echo "2. 查看 Console 标签页中的错误信息"
echo "3. 查看 Network 标签页检查资源加载"
echo ""
read -p "按 Enter 键继续启动调试模式..."
echo ""
echo "正在启动调试模式..."
echo ""

# 设置调试环境变量
export MEEA_DEBUG=1
export ELECTRON_ENABLE_LOGGING=1

# 启动应用程序
if [ -f "MEEA-VIOFO.app/Contents/MacOS/MEEA-VIOFO" ]; then
    open -a "MEEA-VIOFO.app" --args --debug --enable-devtools
    echo "应用程序已启动，调试模式已启用"
else
    echo "错误：未找到 MEEA-VIOFO.app"
    echo "请确保此脚本在应用程序所在目录中运行"
fi

echo ""
read -p "按 Enter 键退出..."
`;

  // macOS启动器路径
  const launcherPath = path.join(projectRoot, 'MEEA-VIOFO-DEBUG.sh');

  // 写入Shell脚本
  fs.writeFileSync(launcherPath, shellContent, 'utf8');

  // 设置执行权限
  fs.chmodSync(launcherPath, '755');

  console.log(`✅ macOS调试启动器已创建: ${launcherPath}`);
  console.log('');
  console.log('📋 使用说明：');
  console.log('1. 将此文件复制到应用程序所在目录');
  console.log('2. 双击运行 MEEA-VIOFO-DEBUG.sh 或在终端中执行');
  console.log('3. 如果页面空白，按 Cmd+Option+I 打开开发者工具');
  console.log('');
}

/**
 * 创建Windows调试启动器
 */
function createWindowsDebugLauncher() {
  console.log('🚀 创建Windows调试启动器...\n');
  
  // 批处理文件内容
  const batchContent = `@echo off
echo ========================================
echo MEEA-VIOFO Windows 调试启动器
echo ========================================
echo.
echo 此启动器将启用以下调试功能：
echo - 开发者工具快捷键 (Ctrl+Shift+I)
echo - 页面刷新快捷键 (Ctrl+R, F5)
echo - 详细的错误日志
echo - 页面加载状态检查
echo.
echo 如果应用显示空白页面，请：
echo 1. 按 Ctrl+Shift+I 打开开发者工具
echo 2. 查看 Console 标签页中的错误信息
echo 3. 查看 Network 标签页检查资源加载
echo.
pause
echo.
echo 正在启动调试模式...
echo.

REM 设置调试环境变量
set MEEA_DEBUG=1
set ELECTRON_ENABLE_LOGGING=1

REM 启动应用程序
start "" "MEEA-VIOFO.exe" --debug --enable-devtools

echo.
echo 应用程序已启动，调试模式已启用
echo 按任意键退出此窗口...
pause > nul
`;

  // Windows启动器路径
  const launcherPath = path.join(projectRoot, 'MEEA-VIOFO-DEBUG.bat');
  
  // 写入批处理文件
  fs.writeFileSync(launcherPath, batchContent, 'utf8');
  
  console.log(`✅ Windows调试启动器已创建: ${launcherPath}`);
  console.log('');
  console.log('📋 使用说明：');
  console.log('1. 将此文件复制到应用程序安装目录');
  console.log('2. 双击运行 MEEA-VIOFO-DEBUG.bat');
  console.log('3. 如果页面空白，按 Ctrl+Shift+I 打开开发者工具');
  console.log('');
  
  // 创建PowerShell版本
  const powershellContent = `# MEEA-VIOFO Windows PowerShell 调试启动器

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MEEA-VIOFO Windows 调试启动器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "此启动器将启用以下调试功能：" -ForegroundColor Yellow
Write-Host "- 开发者工具快捷键 (Ctrl+Shift+I)" -ForegroundColor Green
Write-Host "- 页面刷新快捷键 (Ctrl+R, F5)" -ForegroundColor Green
Write-Host "- 详细的错误日志" -ForegroundColor Green
Write-Host "- 页面加载状态检查" -ForegroundColor Green
Write-Host ""
Write-Host "如果应用显示空白页面，请：" -ForegroundColor Yellow
Write-Host "1. 按 Ctrl+Shift+I 打开开发者工具" -ForegroundColor White
Write-Host "2. 查看 Console 标签页中的错误信息" -ForegroundColor White
Write-Host "3. 查看 Network 标签页检查资源加载" -ForegroundColor White
Write-Host ""

Read-Host "按 Enter 键继续启动调试模式"

Write-Host ""
Write-Host "正在启动调试模式..." -ForegroundColor Green
Write-Host ""

# 设置调试环境变量
$env:MEEA_DEBUG = "1"
$env:ELECTRON_ENABLE_LOGGING = "1"

# 启动应用程序
try {
    Start-Process -FilePath "MEEA-VIOFO.exe" -ArgumentList "--debug", "--enable-devtools"
    Write-Host "应用程序已启动，调试模式已启用" -ForegroundColor Green
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
    Write-Host "请确保 MEEA-VIOFO.exe 在当前目录中" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按 Enter 键退出"
`;

  const powershellPath = path.join(projectRoot, 'MEEA-VIOFO-DEBUG.ps1');
  fs.writeFileSync(powershellPath, powershellContent, 'utf8');
  
  console.log(`✅ PowerShell调试启动器已创建: ${powershellPath}`);
  console.log('');
  
  // 创建说明文档
  const readmeContent = `# Windows 调试启动器使用说明

## 概述

当MEEA-VIOFO在Windows上显示空白页面时，可以使用这些调试启动器来启用开发者工具进行问题排查。

## 文件说明

### MEEA-VIOFO-DEBUG.bat
- Windows批处理文件
- 双击即可运行
- 兼容所有Windows版本

### MEEA-VIOFO-DEBUG.ps1
- PowerShell脚本
- 提供更好的用户界面
- 需要PowerShell支持

## 使用步骤

1. **复制文件**
   - 将调试启动器复制到MEEA-VIOFO.exe所在目录
   - 通常位于: \`C:\\Program Files\\MEEA-VIOFO\\\` 或 \`C:\\Users\\<USER>\\AppData\\Local\\Programs\\MEEA-VIOFO\\\`

2. **运行调试启动器**
   - 双击 \`MEEA-VIOFO-DEBUG.bat\` 或 \`MEEA-VIOFO-DEBUG.ps1\`
   - 按照提示启动应用程序

3. **调试空白页面**
   - 如果页面显示空白，按 \`Ctrl+Shift+I\` 打开开发者工具
   - 查看 Console 标签页中的错误信息
   - 查看 Network 标签页检查资源加载情况

## 调试功能

启用调试模式后，应用程序将具有以下功能：

- ✅ **开发者工具**: \`Ctrl+Shift+I\`
- ✅ **页面刷新**: \`Ctrl+R\` 或 \`F5\`
- ✅ **详细日志**: 控制台输出详细的加载信息
- ✅ **错误捕获**: 自动捕获JavaScript错误和Promise拒绝
- ✅ **加载状态检查**: 自动检查页面和React组件加载状态

## 常见问题排查

### 1. 页面完全空白
- 打开开发者工具查看Console错误
- 检查Network标签页是否有资源加载失败
- 查看是否有JavaScript错误

### 2. 页面部分加载
- 检查React组件是否正确挂载
- 查看是否有CSS样式问题
- 检查API调用是否成功

### 3. 应用程序无法启动
- 检查MEEA-VIOFO.exe是否存在
- 确认Windows版本兼容性
- 检查是否有防病毒软件阻止

## 环境变量

调试启动器会设置以下环境变量：

- \`MEEA_DEBUG=1\`: 启用应用程序调试模式
- \`ELECTRON_ENABLE_LOGGING=1\`: 启用Electron详细日志

## 注意事项

- 调试模式仅用于问题排查，不建议日常使用
- 开发者工具可能会影响应用程序性能
- 调试信息可能包含敏感数据，请谨慎分享

## 技术支持

如果问题仍然存在，请：

1. 使用调试启动器收集错误信息
2. 截图开发者工具中的错误信息
3. 联系技术支持并提供详细的错误日志
`;

  const readmePath = path.join(projectRoot, 'Windows-Debug-README.md');
  fs.writeFileSync(readmePath, readmeContent, 'utf8');
  
  console.log(`✅ 使用说明已创建: ${readmePath}`);
  console.log('');
  console.log('🎯 下一步：');
  console.log('1. 构建Windows应用程序');
  console.log('2. 将调试启动器复制到安装目录');
  console.log('3. 使用调试启动器排查空白页面问题');
}

/**
 * 创建通用调试说明文档
 */
function createUniversalReadme() {
  console.log('📝 创建通用调试说明文档...\n');

  const readmeContent = `# 跨平台调试启动器使用说明

## 概述

当MEEA-VIOFO在不同平台上显示空白页面时，可以使用这些调试启动器来启用开发者工具进行问题排查。

## 平台支持

### Windows
- **MEEA-VIOFO-DEBUG.bat** - Windows批处理文件，双击即可运行
- **MEEA-VIOFO-DEBUG.ps1** - PowerShell脚本，提供更好的用户界面

### macOS
- **MEEA-VIOFO-DEBUG.sh** - Shell脚本，双击或在终端中运行

## 使用步骤

### Windows用户

1. **复制文件**
   - 将 \`MEEA-VIOFO-DEBUG.bat\` 或 \`MEEA-VIOFO-DEBUG.ps1\` 复制到MEEA-VIOFO.exe所在目录
   - 通常位于: \`C:\\Program Files\\MEEA-VIOFO\\\` 或 \`C:\\Users\\<USER>\\AppData\\Local\\Programs\\MEEA-VIOFO\\\`

2. **运行调试启动器**
   - 双击 \`MEEA-VIOFO-DEBUG.bat\` 或 \`MEEA-VIOFO-DEBUG.ps1\`
   - 按照提示启动应用程序

3. **调试空白页面**
   - 如果页面显示空白，按 \`Ctrl+Shift+I\` 打开开发者工具
   - 查看 Console 标签页中的错误信息
   - 查看 Network 标签页检查资源加载情况

### macOS用户

1. **复制文件**
   - 将 \`MEEA-VIOFO-DEBUG.sh\` 复制到MEEA-VIOFO.app所在目录
   - 通常位于: \`/Applications/\` 或下载文件夹

2. **运行调试启动器**
   - 双击 \`MEEA-VIOFO-DEBUG.sh\` 或在终端中执行: \`./MEEA-VIOFO-DEBUG.sh\`
   - 按照提示启动应用程序

3. **调试空白页面**
   - 如果页面显示空白，按 \`Cmd+Option+I\` 打开开发者工具
   - 查看 Console 标签页中的错误信息
   - 查看 Network 标签页检查资源加载情况

## 调试功能

启用调试模式后，应用程序将具有以下功能：

### Windows
- ✅ **开发者工具**: \`Ctrl+Shift+I\`
- ✅ **页面刷新**: \`Ctrl+R\` 或 \`F5\`
- ✅ **详细日志**: 控制台输出详细的加载信息
- ✅ **错误捕获**: 自动捕获JavaScript错误和Promise拒绝

### macOS
- ✅ **开发者工具**: \`Cmd+Option+I\`
- ✅ **页面刷新**: \`Cmd+R\`
- ✅ **详细日志**: 控制台输出详细的加载信息
- ✅ **错误捕获**: 自动捕获JavaScript错误和Promise拒绝

## 常见问题排查

### 1. 页面完全空白
- 打开开发者工具查看Console错误
- 检查Network标签页是否有资源加载失败
- 查看是否有JavaScript错误

### 2. 页面部分加载
- 检查React组件是否正确挂载
- 查看是否有CSS样式问题
- 检查API调用是否成功

### 3. 应用程序无法启动
- **Windows**: 检查MEEA-VIOFO.exe是否存在，确认Windows版本兼容性
- **macOS**: 检查MEEA-VIOFO.app是否存在，确认macOS版本兼容性
- 检查是否有防病毒软件或安全软件阻止

## 环境变量

调试启动器会设置以下环境变量：

- \`MEEA_DEBUG=1\`: 启用应用程序调试模式
- \`ELECTRON_ENABLE_LOGGING=1\`: 启用Electron详细日志

## 注意事项

- 调试模式仅用于问题排查，不建议日常使用
- 开发者工具可能会影响应用程序性能
- 调试信息可能包含敏感数据，请谨慎分享

## 技术支持

如果问题仍然存在，请：

1. 使用调试启动器收集错误信息
2. 截图开发者工具中的错误信息
3. 记录操作系统版本和应用程序版本
4. 联系技术支持并提供详细的错误日志

## 快捷键参考

| 功能 | Windows | macOS |
|------|---------|-------|
| 开发者工具 | \`Ctrl+Shift+I\` | \`Cmd+Option+I\` |
| 页面刷新 | \`Ctrl+R\` 或 \`F5\` | \`Cmd+R\` |
| 强制刷新 | \`Ctrl+Shift+R\` | \`Cmd+Shift+R\` |

---

**版本**: 25.07.18-1805
**更新时间**: ${new Date().toLocaleDateString('zh-CN')}
`;

  const readmePath = path.join(projectRoot, 'Debug-README.md');
  fs.writeFileSync(readmePath, readmeContent, 'utf8');

  console.log(`✅ 通用调试说明已创建: ${readmePath}`);
  console.log('');
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🔧 跨平台调试启动器创建工具

用法:
  node scripts/create-windows-debug-launcher.js [选项]

选项:
  --platform <platform>  指定平台 (windows, macos, all)
  --help, -h            显示此帮助信息

说明:
  此脚本创建调试启动器，用于排查空白页面问题。

  Windows创建的文件：
  - MEEA-VIOFO-DEBUG.bat (批处理启动器)
  - MEEA-VIOFO-DEBUG.ps1 (PowerShell启动器)

  macOS创建的文件：
  - MEEA-VIOFO-DEBUG.sh (Shell脚本启动器)

  通用文件：
  - Debug-README.md (使用说明)
`);
    return;
  }

  const platform = args.find(arg => arg.startsWith('--platform='))?.split('=')[1] ||
                  (args.includes('--platform') ? args[args.indexOf('--platform') + 1] : 'all');

  console.log('🚀 跨平台调试启动器创建工具\n');

  if (platform === 'windows' || platform === 'all') {
    createWindowsDebugLauncher();
  }

  if (platform === 'macos' || platform === 'all') {
    createMacOSDebugLauncher();
  }

  if (platform === 'all') {
    createUniversalReadme();
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  createWindowsDebugLauncher,
  createMacOSDebugLauncher,
  createUniversalReadme
};
