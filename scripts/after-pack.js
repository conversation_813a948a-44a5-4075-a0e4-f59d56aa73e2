const fs = require('fs');
const path = require('path');

module.exports = async function(context) {
  console.log('🔧 After-pack: 处理架构特定资源和权限...');

  const { electronPlatformName, arch, appOutDir } = context;

  console.log('🏗️ 构建信息:', {
    platform: electronPlatformName,
    arch: arch,
    appOutDir: appOutDir
  });

  // 复制架构特定的资源
  await copyArchSpecificResources(context);

  // 确定平台特定的FFmpeg和ExifTool路径
  let ffmpegDir;
  let exiftoolDir;

  if (electronPlatformName === 'darwin') {
    // macOS: Contents/Resources/ffmpeg
    const resourcesPath = path.join(appOutDir, context.packager.appInfo.productFilename + '.app', 'Contents', 'Resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg');
    exiftoolDir = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin');
  } else if (electronPlatformName === 'win32') {
    // Windows: resources/ffmpeg
    const resourcesPath = path.join(appOutDir, 'resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg');
    exiftoolDir = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin');
  } else {
    // Linux: resources/ffmpeg
    const resourcesPath = path.join(appOutDir, 'resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg');
    exiftoolDir = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin');
  }

  console.log('🔧 平台信息:', {
    platform: electronPlatformName,
    arch: context.arch,
    ffmpegDir,
    exiftoolDir
  });

  // 处理FFmpeg权限
  if (fs.existsSync(ffmpegDir)) {
    console.log('🔧 设置FFmpeg权限...');
    setExecutablePermissions(ffmpegDir, ['ffmpeg', 'ffprobe']);
  } else {
    console.warn('⚠️ FFmpeg目录不存在:', ffmpegDir);
  }

  // 处理ExifTool权限
  if (fs.existsSync(exiftoolDir)) {
    console.log('🔧 设置ExifTool权限...');
    setExecutablePermissions(exiftoolDir, ['exiftool']);
  } else {
    console.warn('⚠️ ExifTool目录不存在:', exiftoolDir);
  }

  // 递归设置二进制文件权限
  function setExecutablePermissions(dir, targetFiles = []) {
    if (!fs.existsSync(dir)) {
      return;
    }

    const items = fs.readdirSync(dir);

    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);

      if (stats.isDirectory()) {
        setExecutablePermissions(itemPath, targetFiles);
      } else {
        // 检查是否是目标文件
        const shouldSetPermission = targetFiles.some(target =>
          item === target ||
          item.startsWith(target) ||
          item.endsWith('.exe')
        );

        if (shouldSetPermission) {
          try {
            // Windows下不需要设置执行权限，但我们仍然尝试
            if (process.platform !== 'win32') {
              fs.chmodSync(itemPath, 0o755);
            }
            console.log('✅ 设置执行权限:', itemPath);
          } catch (error) {
            console.error('❌ 设置权限失败:', itemPath, error.message);
          }
        }
      }
    }
  }

  console.log('✅ 二进制文件权限设置完成');
};

// 复制架构特定资源的函数
async function copyArchSpecificResources(context) {
  const { electronPlatformName, arch, appOutDir } = context;

  // 确定平台名称
  let platformName;
  switch (electronPlatformName) {
    case 'win32':
      platformName = 'win';
      break;
    case 'darwin':
      platformName = 'mac';
      break;
    case 'linux':
      platformName = 'linux';
      break;
    default:
      console.warn('⚠️ 未知平台:', electronPlatformName);
      return;
  }

  // 确定资源目录路径
  let resourcesDir;
  if (electronPlatformName === 'darwin') {
    // macOS: Contents/Resources
    const appName = context.packager.appInfo.productFilename;
    resourcesDir = path.join(appOutDir, `${appName}.app`, 'Contents', 'Resources');
  } else {
    // Windows/Linux: resources
    resourcesDir = path.join(appOutDir, 'resources');
  }

  console.log('📦 复制架构特定资源:');
  console.log('  平台:', platformName);
  console.log('  架构:', arch);
  console.log('  资源目录:', resourcesDir);

  // 复制 FFmpeg
  const ffmpegSourceDir = path.join(__dirname, '..', 'ffmpeg', `${platformName}-${arch}`);
  const ffmpegTargetDir = path.join(resourcesDir, 'ffmpeg', `${platformName}-${arch}`);

  if (fs.existsSync(ffmpegSourceDir)) {
    console.log('📦 复制 FFmpeg:', ffmpegSourceDir, '->', ffmpegTargetDir);
    copyDirectory(ffmpegSourceDir, ffmpegTargetDir);
    console.log('✅ FFmpeg 复制完成');
  } else {
    console.warn('⚠️ FFmpeg 源目录不存在:', ffmpegSourceDir);
  }

  // 复制 ExifTool (仅 Windows)
  if (electronPlatformName === 'win32') {
    const exiftoolSourceDir = path.join(__dirname, '..', 'exiftool', `win-${arch}`);
    const exiftoolTargetDir = path.join(resourcesDir, 'exiftool', `win-${arch}`);

    if (fs.existsSync(exiftoolSourceDir)) {
      console.log('📦 复制 ExifTool:', exiftoolSourceDir, '->', exiftoolTargetDir);
      copyDirectory(exiftoolSourceDir, exiftoolTargetDir);
      console.log('✅ ExifTool 复制完成');
    } else {
      console.warn('⚠️ ExifTool 源目录不存在:', exiftoolSourceDir);
    }
  }
}

// 递归复制目录
function copyDirectory(source, target) {
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  const items = fs.readdirSync(source);

  for (const item of items) {
    const sourcePath = path.join(source, item);
    const targetPath = path.join(target, item);

    const stats = fs.statSync(sourcePath);

    if (stats.isDirectory()) {
      copyDirectory(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);

      // 在 Unix 系统上保持执行权限
      if (process.platform !== 'win32') {
        const mode = stats.mode;
        fs.chmodSync(targetPath, mode);
      }
    }
  }
}