const fs = require('fs');
const path = require('path');

module.exports = async function(context) {
  console.log('🔧 After-pack: 处理架构特定资源和权限...');

  const { electronPlatformName, arch, appOutDir } = context;

  console.log('🏗️ 构建信息:', {
    platform: electronPlatformName,
    arch: arch,
    appOutDir: appOutDir
  });

  // 映射平台名称
  let platformName;
  if (electronPlatformName === 'darwin') {
    platformName = 'mac';
  } else if (electronPlatformName === 'win32') {
    platformName = 'win';
  } else {
    platformName = 'linux';
  }

  // 映射架构名称（确保是字符串格式）
  let archName;
  if (arch === 0 || arch === 1 || arch === 'x64') {
    archName = 'x64';
  } else if (arch === 3 || arch === 'arm64') {
    archName = 'arm64';
  } else {
    archName = String(arch); // 回退到字符串转换
  }

  // 确定平台特定的FFmpeg和ExifTool路径
  let ffmpegDir;
  let exiftoolDir;

  if (electronPlatformName === 'darwin') {
    // macOS: Contents/Resources/ffmpeg/mac-arch
    const resourcesPath = path.join(appOutDir, context.packager.appInfo.productFilename + '.app', 'Contents', 'Resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${archName}`);
    exiftoolDir = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin');
  } else if (electronPlatformName === 'win32') {
    // Windows: resources/ffmpeg/win-arch
    const resourcesPath = path.join(appOutDir, 'resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${archName}`);
    exiftoolDir = path.join(resourcesPath, 'exiftool', `${platformName}-${archName}`);
  } else {
    // Linux: resources/ffmpeg/linux-arch
    const resourcesPath = path.join(appOutDir, 'resources');
    ffmpegDir = path.join(resourcesPath, 'ffmpeg', `${platformName}-${archName}`);
    exiftoolDir = path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'exiftool-vendored.pl', 'bin');
  }

  console.log('🔧 平台信息:', {
    platform: electronPlatformName,
    arch: context.arch,
    ffmpegDir,
    exiftoolDir
  });

  // 处理FFmpeg权限
  if (fs.existsSync(ffmpegDir)) {
    console.log('🔧 设置FFmpeg权限...');
    setExecutablePermissions(ffmpegDir, ['ffmpeg', 'ffprobe']);
  } else {
    console.warn('⚠️ FFmpeg目录不存在:', ffmpegDir);
  }

  // 处理ExifTool权限
  if (fs.existsSync(exiftoolDir)) {
    console.log('🔧 设置ExifTool权限...');
    setExecutablePermissions(exiftoolDir, ['exiftool']);
  } else {
    console.warn('⚠️ ExifTool目录不存在:', exiftoolDir);
  }

  // 递归设置二进制文件权限
  function setExecutablePermissions(dir, targetFiles = []) {
    if (!fs.existsSync(dir)) {
      return;
    }

    const items = fs.readdirSync(dir);

    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);

      if (stats.isDirectory()) {
        setExecutablePermissions(itemPath, targetFiles);
      } else {
        // 检查是否是目标文件
        const shouldSetPermission = targetFiles.some(target =>
          item === target ||
          item.startsWith(target) ||
          item.endsWith('.exe')
        );

        if (shouldSetPermission) {
          try {
            // Windows下不需要设置执行权限，但我们仍然尝试
            if (process.platform !== 'win32') {
              fs.chmodSync(itemPath, 0o755);
            }
            console.log('✅ 设置执行权限:', itemPath);
          } catch (error) {
            console.error('❌ 设置权限失败:', itemPath, error.message);
          }
        }
      }
    }
  }

  console.log('✅ 二进制文件权限设置完成');
};

// 复制架构特定资源的函数
