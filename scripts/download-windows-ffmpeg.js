#!/usr/bin/env node

/**
 * Windows FFmpeg 下载脚本
 * 
 * 此脚本下载Windows平台的FFmpeg二进制文件
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

const projectRoot = path.join(__dirname, '..');
const ffmpegDir = path.join(projectRoot, 'ffmpeg');

// FFmpeg下载URL配置
const FFMPEG_URLS = {
  'win-x64': {
    url: 'https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip',
    binPath: 'ffmpeg-master-latest-win64-gpl/bin'
  },
  'win-arm64': {
    // Windows ARM64使用x64版本（通过模拟运行）
    url: 'https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip',
    binPath: 'ffmpeg-master-latest-win64-gpl/bin'
  }
};

/**
 * 下载文件
 */
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`📥 下载: ${url}`);

    const file = fs.createWriteStream(outputPath);
    const startTime = Date.now();
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // 处理重定向
        file.close();
        fs.unlinkSync(outputPath);
        return downloadFile(response.headers.location, outputPath)
          .then(resolve)
          .catch(reject);
      }
      
      if (response.statusCode !== 200) {
        file.close();
        fs.unlinkSync(outputPath);
        return reject(new Error(`下载失败: HTTP ${response.statusCode}`));
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const percent = ((downloadedSize / totalSize) * 100).toFixed(1);
          const downloadedMB = (downloadedSize / 1024 / 1024).toFixed(1);
          const totalMB = (totalSize / 1024 / 1024).toFixed(1);
          const speed = downloadedSize / ((Date.now() - startTime) / 1000) / 1024 / 1024;
          process.stdout.write(`\r📊 进度: ${percent}% (${downloadedMB}MB / ${totalMB}MB) 速度: ${speed.toFixed(1)}MB/s`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log('\n✅ 下载完成');
        resolve();
      });
      
      file.on('error', (err) => {
        file.close();
        fs.unlinkSync(outputPath);
        reject(err);
      });
    }).on('error', (err) => {
      file.close();
      fs.unlinkSync(outputPath);
      reject(err);
    });
  });
}

/**
 * 解压ZIP文件
 */
function extractZip(zipPath, extractPath) {
  console.log(`📦 解压: ${zipPath}`);
  
  try {
    // 确保解压目录存在
    if (!fs.existsSync(extractPath)) {
      fs.mkdirSync(extractPath, { recursive: true });
    }
    
    // 使用系统的解压工具
    if (process.platform === 'win32') {
      // Windows: 使用PowerShell
      execSync(`powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${extractPath}' -Force"`, { stdio: 'inherit' });
    } else {
      // macOS/Linux: 使用unzip
      execSync(`unzip -o "${zipPath}" -d "${extractPath}"`, { stdio: 'inherit' });
    }
    
    console.log('✅ 解压完成');
  } catch (error) {
    throw new Error(`解压失败: ${error.message}`);
  }
}

/**
 * 复制FFmpeg二进制文件
 */
function copyFFmpegBinaries(sourcePath, targetPath) {
  console.log(`📋 复制二进制文件: ${sourcePath} -> ${targetPath}`);
  
  if (!fs.existsSync(sourcePath)) {
    throw new Error(`源目录不存在: ${sourcePath}`);
  }
  
  // 确保目标目录存在
  if (!fs.existsSync(targetPath)) {
    fs.mkdirSync(targetPath, { recursive: true });
  }
  
  // 复制ffmpeg.exe和ffprobe.exe
  const binaries = ['ffmpeg.exe', 'ffprobe.exe'];
  
  for (const binary of binaries) {
    const sourceFile = path.join(sourcePath, binary);
    const targetFile = path.join(targetPath, binary);
    
    if (fs.existsSync(sourceFile)) {
      fs.copyFileSync(sourceFile, targetFile);
      const stats = fs.statSync(targetFile);
      console.log(`✅ 复制 ${binary}: ${(stats.size / 1024 / 1024).toFixed(1)}MB`);
    } else {
      console.warn(`⚠️ 未找到 ${binary} 在 ${sourcePath}`);
    }
  }
}

/**
 * 下载指定架构的FFmpeg
 */
async function downloadFFmpegForArch(arch) {
  const platformKey = `win-${arch}`;
  const config = FFMPEG_URLS[platformKey];
  
  if (!config) {
    throw new Error(`不支持的架构: ${arch}`);
  }
  
  console.log(`\n🚀 开始下载 Windows ${arch} FFmpeg...`);
  
  // 创建临时目录
  const tempDir = path.join(projectRoot, 'temp', `ffmpeg-${arch}`);
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  const zipPath = path.join(tempDir, 'ffmpeg.zip');
  const extractPath = path.join(tempDir, 'extracted');
  const targetPath = path.join(ffmpegDir, platformKey);
  
  try {
    // 下载
    await downloadFile(config.url, zipPath);
    
    // 解压
    extractZip(zipPath, extractPath);
    
    // 查找二进制文件目录
    const binSourcePath = path.join(extractPath, config.binPath);
    
    // 复制二进制文件
    copyFFmpegBinaries(binSourcePath, targetPath);
    
    console.log(`✅ Windows ${arch} FFmpeg 下载完成!`);
    
  } catch (error) {
    console.error(`❌ Windows ${arch} FFmpeg 下载失败:`, error.message);
    throw error;
  } finally {
    // 清理临时文件
    try {
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (cleanupError) {
      console.warn('⚠️ 清理临时文件失败:', cleanupError.message);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📥 Windows FFmpeg 下载脚本

用法:
  node scripts/download-windows-ffmpeg.js [选项]

选项:
  --help, -h     显示此帮助信息
  --x64          只下载x64版本
  --arm64        只下载ARM64版本
  --all          下载所有架构（默认）

说明:
  此脚本从GitHub下载Windows平台的FFmpeg二进制文件。
  下载的文件将保存到 ffmpeg/win-{arch}/ 目录中。
`);
    return;
  }
  
  try {
    console.log('🚀 开始下载Windows FFmpeg...\n');
    
    let architectures = [];
    
    if (args.includes('--x64')) {
      architectures = ['x64'];
    } else if (args.includes('--arm64')) {
      architectures = ['arm64'];
    } else {
      architectures = ['x64', 'arm64'];
    }
    
    for (const arch of architectures) {
      await downloadFFmpegForArch(arch);
    }
    
    console.log('\n🎉 所有Windows FFmpeg下载完成！');
    console.log('\n💡 验证下载结果:');
    console.log('   运行: yarn verify:ffmpeg');
    
  } catch (error) {
    console.error('\n❌ 下载失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  downloadFFmpegForArch,
  downloadFile,
  extractZip,
  copyFFmpegBinaries
};
