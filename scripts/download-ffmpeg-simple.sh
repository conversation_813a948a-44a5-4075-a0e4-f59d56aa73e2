#!/bin/bash

# 简单的FFmpeg下载脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEMP_DIR="$PROJECT_ROOT/temp/ffmpeg-download"
FFMPEG_DIR="$PROJECT_ROOT/ffmpeg"

# 创建临时目录
mkdir -p "$TEMP_DIR"
mkdir -p "$FFMPEG_DIR/win-x64"
mkdir -p "$FFMPEG_DIR/win-arm64"

echo "🚀 开始下载Windows FFmpeg..."

# 下载Windows x64版本
echo "📥 下载Windows x64 FFmpeg..."
cd "$TEMP_DIR"

# 使用curl下载
curl -L -o ffmpeg-win64.zip "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"

echo "📦 解压FFmpeg..."
unzip -q ffmpeg-win64.zip

# 查找bin目录
BIN_DIR=$(find . -name "bin" -type d | head -1)
if [ -z "$BIN_DIR" ]; then
    echo "❌ 未找到bin目录"
    exit 1
fi

echo "📋 复制二进制文件到win-x64..."
cp "$BIN_DIR/ffmpeg.exe" "$FFMPEG_DIR/win-x64/"
cp "$BIN_DIR/ffprobe.exe" "$FFMPEG_DIR/win-x64/"

echo "📋 复制二进制文件到win-arm64..."
cp "$BIN_DIR/ffmpeg.exe" "$FFMPEG_DIR/win-arm64/"
cp "$BIN_DIR/ffprobe.exe" "$FFMPEG_DIR/win-arm64/"

# 清理临时文件
echo "🧹 清理临时文件..."
cd "$PROJECT_ROOT"
rm -rf "$TEMP_DIR"

echo "✅ Windows FFmpeg下载完成！"

# 验证文件
echo "🔍 验证下载的文件..."
ls -la "$FFMPEG_DIR/win-x64/"
ls -la "$FFMPEG_DIR/win-arm64/"

echo "💡 运行验证脚本:"
echo "   node scripts/verify-ffmpeg-binaries.js"
