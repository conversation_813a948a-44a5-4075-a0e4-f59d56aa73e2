# 第三方依赖下载指南

## 概述

本项目需要为不同平台下载FFmpeg和ExifTool依赖。每个平台的构建产物只包含对应平台和架构的工具，确保构建产物体积最小化。

## 1. FFmpeg 下载

### Windows 平台

**下载地址：**
```
https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip
```

**操作步骤：**
1. 下载 `ffmpeg-master-latest-win64-gpl.zip` (约147MB)
2. 解压文件
3. 在解压后的文件夹中找到 `ffmpeg-master-latest-win64-gpl/bin/` 目录
4. 复制以下文件：
   - `ffmpeg.exe` → `ffmpeg/win-x64/ffmpeg.exe`
   - `ffprobe.exe` → `ffmpeg/win-x64/ffprobe.exe`
   - `ffmpeg.exe` → `ffmpeg/win-arm64/ffmpeg.exe` (ARM64使用x64版本)
   - `ffprobe.exe` → `ffmpeg/win-arm64/ffprobe.exe` (ARM64使用x64版本)

### macOS 平台

**Intel x64:**
```
https://evermeet.cx/ffmpeg/ffmpeg-6.1.zip
https://evermeet.cx/ffmpeg/ffprobe-6.1.zip
```

**Apple Silicon (ARM64):**
```
https://www.osxexperts.net/ffmpeg6arm.html
```
或者使用Homebrew安装后复制：
```bash
brew install ffmpeg
# 然后从 /opt/homebrew/bin/ (ARM64) 或 /usr/local/bin/ (Intel) 复制文件
```

**操作步骤：**
1. 下载对应架构的FFmpeg文件
2. 解压并复制：
   - Intel版本 → `ffmpeg/mac-x64/ffmpeg` 和 `ffmpeg/mac-x64/ffprobe`
   - ARM64版本 → `ffmpeg/mac-arm64/ffmpeg` 和 `ffmpeg/mac-arm64/ffprobe`
3. 设置执行权限：
   ```bash
   chmod +x ffmpeg/mac-*/ffmpeg
   chmod +x ffmpeg/mac-*/ffprobe
   ```

### Linux 平台

**下载地址：**
```
# x64版本
https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz

# ARM64版本
https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz
```

**操作步骤：**
1. 下载对应架构的文件
2. 解压：
   ```bash
   tar -xf ffmpeg-release-amd64-static.tar.xz
   tar -xf ffmpeg-release-arm64-static.tar.xz
   ```
3. 复制文件：
   - x64版本 → `ffmpeg/linux-x64/ffmpeg` 和 `ffmpeg/linux-x64/ffprobe`
   - ARM64版本 → `ffmpeg/linux-arm64/ffmpeg` 和 `ffmpeg/linux-arm64/ffprobe`
4. 设置执行权限：
   ```bash
   chmod +x ffmpeg/linux-*/ffmpeg
   chmod +x ffmpeg/linux-*/ffprobe
   ```

## 2. ExifTool 下载

### Windows 平台

**下载地址：**
```
https://exiftool.org/exiftool-12.70.zip
```

**操作步骤：**
1. 下载 `exiftool-12.70.zip`
2. 解压文件
3. 将 `exiftool(-k).exe` 重命名为 `exiftool.exe`
4. 复制到：
   - `exiftool/win-x64/exiftool.exe`
   - `exiftool/win-arm64/exiftool.exe`

### macOS 和 Linux 平台

ExifTool在macOS和Linux上通过npm包 `exiftool-vendored` 提供，不需要手动下载。

## 3. 完整目录结构

下载完成后，您的项目目录结构应该是：

```
ffmpeg/
├── win-x64/
│   ├── ffmpeg.exe          (~70-100MB)
│   └── ffprobe.exe         (~70-100MB)
├── win-arm64/
│   ├── ffmpeg.exe          (~70-100MB)
│   └── ffprobe.exe         (~70-100MB)
├── mac-x64/
│   ├── ffmpeg              (~70-100MB)
│   └── ffprobe             (~70-100MB)
├── mac-arm64/
│   ├── ffmpeg              (~70-100MB)
│   └── ffprobe             (~70-100MB)
├── linux-x64/
│   ├── ffmpeg              (~70-100MB)
│   └── ffprobe             (~70-100MB)
└── linux-arm64/
    ├── ffmpeg              (~70-100MB)
    └── ffprobe             (~70-100MB)

exiftool/
├── win-x64/
│   └── exiftool.exe        (~10-15MB)
└── win-arm64/
    └── exiftool.exe        (~10-15MB)
```

## 4. 自动化下载脚本

如果您想使用自动化脚本，项目中提供了以下脚本：

```bash
# 下载Windows FFmpeg（简单版本，推荐）
yarn download:ffmpeg:windows:simple

# 下载Windows FFmpeg（Node.js版本）
yarn download:ffmpeg:windows

# 验证所有下载的文件
yarn verify:ffmpeg
yarn verify:platform-resources
```

## 5. 验证下载

下载完成后，运行以下命令验证：

```bash
# 验证FFmpeg二进制文件
yarn verify:ffmpeg

# 验证平台特定资源
yarn verify:platform-resources

# 测试构建配置
yarn test:build-config
```

**验证成功的输出示例：**
```
✅ win-x64: ffmpeg.exe (85.2MB), ffprobe.exe (85.1MB)
✅ win-arm64: ffmpeg.exe (85.2MB), ffprobe.exe (85.1MB)
✅ mac-x64: ffmpeg (89.1MB), ffprobe (89.0MB)
✅ mac-arm64: ffmpeg (87.3MB), ffprobe (87.2MB)
```

## 6. 构建特定平台

下载完成后，您可以构建特定平台的应用：

```bash
# 构建Windows版本
yarn dist:win:x64      # Windows x64
yarn dist:win:arm64    # Windows ARM64

# 构建macOS版本
yarn dist:mac:x64      # macOS Intel
yarn dist:mac:arm64    # macOS Apple Silicon

# 构建Linux版本
yarn dist:linux:x64    # Linux x64
yarn dist:linux:arm64  # Linux ARM64

# 构建所有平台
yarn dist:all
```

## 7. 常见问题

### 文件权限问题（macOS/Linux）
```bash
# 如果遇到权限问题，运行：
chmod +x ffmpeg/mac-*/ffmpeg
chmod +x ffmpeg/mac-*/ffprobe
chmod +x ffmpeg/linux-*/ffmpeg
chmod +x ffmpeg/linux-*/ffprobe
```

### 文件大小检查
- 如果文件大小明显偏小（几KB），说明下载不完整
- 正常的FFmpeg文件应该在70-100MB左右
- ExifTool应该在10-15MB左右

### Windows ARM64 说明
- Windows ARM64使用x64版本的FFmpeg（通过模拟运行）
- 这是因为原生ARM64版本的FFmpeg构建较少，但x64版本可以在ARM64 Windows上正常运行

### 构建错误排查
如果遇到"FFmpeg文件损坏"错误：
1. 检查文件是否存在且大小正常
2. 运行 `yarn verify:ffmpeg` 检查文件状态
3. 重新下载对应平台的文件
4. 确保文件权限正确（Unix系统）

## 8. 镜像下载地址（如果官方地址较慢）

### FFmpeg 镜像
```
# GitHub镜像
https://mirror.ghproxy.com/https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip

# 或者使用其他CDN
https://cdn.jsdelivr.net/gh/BtbN/FFmpeg-Builds@latest/
```

### ExifTool 镜像
```
# CPAN镜像
https://cpan.metacpan.org/authors/id/E/EX/EXIFTOOL/Image-ExifTool-12.70.tar.gz
```

## 9. 开发环境设置

对于开发环境，您也可以：

1. **使用系统安装的工具：**
   ```bash
   # macOS
   brew install ffmpeg exiftool
   
   # Ubuntu/Debian
   sudo apt install ffmpeg exiftool
   
   # Windows (使用Chocolatey)
   choco install ffmpeg exiftool
   ```

2. **设置环境变量：**
   开发环境下，代码会自动检测系统安装的工具作为回退选项。

## 10. 更新依赖

定期检查并更新依赖：

```bash
# 检查FFmpeg版本
./ffmpeg/win-x64/ffmpeg.exe -version
./ffmpeg/mac-x64/ffmpeg -version

# 检查ExifTool版本
./exiftool/win-x64/exiftool.exe -ver
```

建议每3-6个月检查一次新版本，特别是安全更新。

---

**注意：** 完成所有下载后，每个平台的构建产物将只包含对应平台和架构的工具，大大减少了应用程序的体积。
