# 快速开始指南

## 1. 环境准备

确保您的系统已安装：
- **Node.js** (v16+)
- **Yarn** 包管理器
- **Git**

## 2. 克隆项目

```bash
git clone <repository-url>
cd meea-viofo-all
```

## 3. 安装依赖

```bash
# 安装Node.js依赖
yarn install
```

## 4. 下载第三方工具

**重要：** 项目需要FFmpeg和ExifTool等第三方工具。

### 快速检查
```bash
# 检查依赖状态
yarn setup:deps
```

### 手动下载
如果检查失败，请参考 **[第三方依赖下载指南](dependencies-download-guide.md)** 手动下载所需文件。

### 自动下载（Windows）
```bash
# 仅适用于Windows平台
yarn download:ffmpeg:windows:simple
```

## 5. 开发模式

```bash
# 启动开发服务器
yarn dev
```

这将同时启动：
- Vite开发服务器（前端）
- Electron应用程序

## 6. 构建应用

### 构建前端
```bash
yarn build
```

### 构建特定平台
```bash
# Windows
yarn dist:win:x64
yarn dist:win:arm64

# macOS
yarn dist:mac:x64
yarn dist:mac:arm64

# Linux
yarn dist:linux:x64
yarn dist:linux:arm64

# 所有平台
yarn dist:all
```

## 7. 验证构建

```bash
# 验证FFmpeg文件
yarn verify:ffmpeg

# 验证平台资源
yarn verify:platform-resources

# 测试构建配置
yarn test:build-config
```

## 8. 常见问题

### FFmpeg文件缺失
```
❌ 错误：FFmpeg文件损坏或缺失
```

**解决方案：**
1. 参考 [第三方依赖下载指南](dependencies-download-guide.md)
2. 运行 `yarn verify:ffmpeg` 检查状态
3. 手动下载对应平台的FFmpeg文件

### 权限问题（macOS/Linux）
```
❌ 错误：Permission denied
```

**解决方案：**
```bash
chmod +x ffmpeg/mac-*/ffmpeg
chmod +x ffmpeg/mac-*/ffprobe
chmod +x ffmpeg/linux-*/ffmpeg
chmod +x ffmpeg/linux-*/ffprobe
```

### 构建失败
```
❌ 错误：构建过程中出现错误
```

**解决方案：**
1. 确保所有依赖文件已正确下载
2. 运行 `yarn verify:platform-resources`
3. 检查构建日志中的具体错误信息
4. 参考 [故障排除指南](TROUBLESHOOTING.md)

## 9. 开发工具

### 有用的脚本
```bash
# 清理构建产物
yarn clean

# 重新安装依赖
yarn install --force

# 检查代码风格
yarn lint

# 运行测试
yarn test
```

### 调试模式
```bash
# 启用详细日志
DEBUG=* yarn dev

# 启用Electron调试
yarn dev --inspect
```

## 10. 项目结构

```
meea-viofo-all/
├── src/                    # 前端源码
├── electron/              # Electron主进程代码
├── ffmpeg/                # FFmpeg二进制文件
│   ├── win-x64/
│   ├── win-arm64/
│   ├── mac-x64/
│   ├── mac-arm64/
│   ├── linux-x64/
│   └── linux-arm64/
├── exiftool/              # ExifTool二进制文件
│   ├── win-x64/
│   └── win-arm64/
├── scripts/               # 构建和工具脚本
├── docs/                  # 文档
└── dist/                  # 构建产物
```

## 11. 下一步

- 阅读 [开发指南](DEVELOPMENT.md)
- 查看 [API文档](API.md)
- 了解 [贡献指南](CONTRIBUTING.md)

---

如果遇到问题，请：
1. 查看 [故障排除指南](TROUBLESHOOTING.md)
2. 搜索 [Issues](https://github.com/your-repo/issues)
3. 创建新的Issue报告问题
