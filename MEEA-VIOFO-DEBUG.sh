#!/bin/bash

echo "========================================"
echo "MEEA-VIOFO macOS 调试启动器"
echo "========================================"
echo ""
echo "此启动器将启用以下调试功能："
echo "- 开发者工具快捷键 (Cmd+Option+I)"
echo "- 页面刷新快捷键 (Cmd+R)"
echo "- 详细的错误日志"
echo "- 页面加载状态检查"
echo ""
echo "如果应用显示空白页面，请："
echo "1. 按 Cmd+Option+I 打开开发者工具"
echo "2. 查看 Console 标签页中的错误信息"
echo "3. 查看 Network 标签页检查资源加载"
echo ""
read -p "按 Enter 键继续启动调试模式..."
echo ""
echo "正在启动调试模式..."
echo ""

# 设置调试环境变量
export MEEA_DEBUG=1
export ELECTRON_ENABLE_LOGGING=1

# 启动应用程序
if [ -f "MEEA-VIOFO.app/Contents/MacOS/MEEA-VIOFO" ]; then
    open -a "MEEA-VIOFO.app" --args --debug --enable-devtools
    echo "应用程序已启动，调试模式已启用"
else
    echo "错误：未找到 MEEA-VIOFO.app"
    echo "请确保此脚本在应用程序所在目录中运行"
fi

echo ""
read -p "按 Enter 键退出..."
