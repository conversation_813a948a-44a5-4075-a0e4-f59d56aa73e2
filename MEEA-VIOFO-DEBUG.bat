@echo off
echo ========================================
echo MEEA-VIOFO Windows 调试启动器
echo ========================================
echo.
echo 此启动器将启用以下调试功能：
echo - 开发者工具快捷键 (Ctrl+Shift+I)
echo - 页面刷新快捷键 (Ctrl+R, F5)
echo - 详细的错误日志
echo - 页面加载状态检查
echo.
echo 如果应用显示空白页面，请：
echo 1. 按 Ctrl+Shift+I 打开开发者工具
echo 2. 查看 Console 标签页中的错误信息
echo 3. 查看 Network 标签页检查资源加载
echo.
pause
echo.
echo 正在启动调试模式...
echo.

REM 设置调试环境变量
set MEEA_DEBUG=1
set ELECTRON_ENABLE_LOGGING=1

REM 启动应用程序
start "" "MEEA-VIOFO.exe" --debug --enable-devtools

echo.
echo 应用程序已启动，调试模式已启用
echo 按任意键退出此窗口...
pause > nul
