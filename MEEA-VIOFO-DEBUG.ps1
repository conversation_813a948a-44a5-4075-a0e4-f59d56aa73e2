# MEEA-VIOFO Windows PowerShell 调试启动器

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MEEA-VIOFO Windows 调试启动器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "此启动器将启用以下调试功能：" -ForegroundColor Yellow
Write-Host "- 开发者工具快捷键 (Ctrl+Shift+I)" -ForegroundColor Green
Write-Host "- 页面刷新快捷键 (Ctrl+R, F5)" -ForegroundColor Green
Write-Host "- 详细的错误日志" -ForegroundColor Green
Write-Host "- 页面加载状态检查" -ForegroundColor Green
Write-Host ""
Write-Host "如果应用显示空白页面，请：" -ForegroundColor Yellow
Write-Host "1. 按 Ctrl+Shift+I 打开开发者工具" -ForegroundColor White
Write-Host "2. 查看 Console 标签页中的错误信息" -ForegroundColor White
Write-Host "3. 查看 Network 标签页检查资源加载" -ForegroundColor White
Write-Host ""

Read-Host "按 Enter 键继续启动调试模式"

Write-Host ""
Write-Host "正在启动调试模式..." -ForegroundColor Green
Write-Host ""

# 设置调试环境变量
$env:MEEA_DEBUG = "1"
$env:ELECTRON_ENABLE_LOGGING = "1"

# 启动应用程序
try {
    Start-Process -FilePath "MEEA-VIOFO.exe" -ArgumentList "--debug", "--enable-devtools"
    Write-Host "应用程序已启动，调试模式已启用" -ForegroundColor Green
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
    Write-Host "请确保 MEEA-VIOFO.exe 在当前目录中" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按 Enter 键退出"
