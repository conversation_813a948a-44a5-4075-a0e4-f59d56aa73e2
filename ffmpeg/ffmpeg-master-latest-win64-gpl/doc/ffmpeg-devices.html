<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Devices Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Devices Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Device-Options" href="#Device-Options">2 Device Options</a></li>
  <li><a id="toc-Input-Devices" href="#Input-Devices">3 Input Devices</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-alsa" href="#alsa">3.1 alsa</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options" href="#Options">3.1.1 Options</a></li>
    </ul></li>
    <li><a id="toc-android_005fcamera" href="#android_005fcamera">3.2 android_camera</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-1" href="#Options-1">3.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-avfoundation" href="#avfoundation">3.3 avfoundation</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-2" href="#Options-2">3.3.1 Options</a></li>
      <li><a id="toc-Examples" href="#Examples">3.3.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-decklink" href="#decklink">3.4 decklink</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-3" href="#Options-3">3.4.1 Options</a></li>
      <li><a id="toc-Examples-1" href="#Examples-1">3.4.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-dshow" href="#dshow">3.5 dshow</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-4" href="#Options-4">3.5.1 Options</a></li>
      <li><a id="toc-Examples-2" href="#Examples-2">3.5.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-fbdev" href="#fbdev">3.6 fbdev</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-5" href="#Options-5">3.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-gdigrab" href="#gdigrab">3.7 gdigrab</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-6" href="#Options-6">3.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-iec61883" href="#iec61883">3.8 iec61883</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-7" href="#Options-7">3.8.1 Options</a></li>
      <li><a id="toc-Examples-3" href="#Examples-3">3.8.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-jack" href="#jack">3.9 jack</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-8" href="#Options-8">3.9.1 Options</a></li>
    </ul></li>
    <li><a id="toc-kmsgrab" href="#kmsgrab">3.10 kmsgrab</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-9" href="#Options-9">3.10.1 Options</a></li>
      <li><a id="toc-Examples-4" href="#Examples-4">3.10.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-lavfi" href="#lavfi">3.11 lavfi</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-10" href="#Options-10">3.11.1 Options</a></li>
      <li><a id="toc-Examples-5" href="#Examples-5">3.11.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-libcdio" href="#libcdio">3.12 libcdio</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-11" href="#Options-11">3.12.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libdc1394" href="#libdc1394">3.13 libdc1394</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-12" href="#Options-12">3.13.1 Options</a></li>
    </ul></li>
    <li><a id="toc-openal" href="#openal">3.14 openal</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-13" href="#Options-13">3.14.1 Options</a></li>
      <li><a id="toc-Examples-6" href="#Examples-6">3.14.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-oss" href="#oss">3.15 oss</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-14" href="#Options-14">3.15.1 Options</a></li>
    </ul></li>
    <li><a id="toc-pulse" href="#pulse">3.16 pulse</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-15" href="#Options-15">3.16.1 Options</a></li>
      <li><a id="toc-Examples-7" href="#Examples-7">3.16.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-sndio" href="#sndio">3.17 sndio</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-16" href="#Options-16">3.17.1 Options</a></li>
    </ul></li>
    <li><a id="toc-video4linux2_002c-v4l2" href="#video4linux2_002c-v4l2">3.18 video4linux2, v4l2</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-17" href="#Options-17">3.18.1 Options</a></li>
    </ul></li>
    <li><a id="toc-vfwcap" href="#vfwcap">3.19 vfwcap</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-18" href="#Options-18">3.19.1 Options</a></li>
    </ul></li>
    <li><a id="toc-x11grab" href="#x11grab">3.20 x11grab</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-19" href="#Options-19">3.20.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Output-Devices" href="#Output-Devices">4 Output Devices</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-alsa-1" href="#alsa-1">4.1 alsa</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-8" href="#Examples-8">4.1.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-AudioToolbox" href="#AudioToolbox">4.2 AudioToolbox</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-20" href="#Options-20">4.2.1 Options</a></li>
      <li><a id="toc-Examples-9" href="#Examples-9">4.2.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-caca" href="#caca">4.3 caca</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-21" href="#Options-21">4.3.1 Options</a></li>
      <li><a id="toc-Examples-10" href="#Examples-10">4.3.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-decklink-1" href="#decklink-1">4.4 decklink</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-22" href="#Options-22">4.4.1 Options</a></li>
      <li><a id="toc-Examples-11" href="#Examples-11">4.4.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-fbdev-1" href="#fbdev-1">4.5 fbdev</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-23" href="#Options-23">4.5.1 Options</a></li>
      <li><a id="toc-Examples-12" href="#Examples-12">4.5.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-oss-1" href="#oss-1">4.6 oss</a></li>
    <li><a id="toc-pulse-1" href="#pulse-1">4.7 pulse</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-24" href="#Options-24">4.7.1 Options</a></li>
      <li><a id="toc-Examples-13" href="#Examples-13">4.7.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-sndio-1" href="#sndio-1">4.8 sndio</a></li>
    <li><a id="toc-v4l2" href="#v4l2">4.9 v4l2</a></li>
    <li><a id="toc-xv" href="#xv">4.10 xv</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-25" href="#Options-25">4.10.1 Options</a></li>
      <li><a id="toc-Examples-14" href="#Examples-14">4.10.2 Examples</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-See-Also" href="#See-Also">5 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">6 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the input and output devices provided by the
libavdevice library.
</p>

<a name="Device-Options"></a>
<h2 class="chapter">2 Device Options<span class="pull-right"><a class="anchor hidden-xs" href="#Device-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Device-Options" aria-hidden="true">TOC</a></span></h2>

<p>The libavdevice library provides the same interface as
libavformat. Namely, an input device is considered like a demuxer, and
an output device like a muxer, and the interface and generic device
options are the same provided by libavformat (see the ffmpeg-formats
manual).
</p>
<p>In addition each input or output device may support so-called private
options, which are specific for that component.
</p>
<p>Options may be set by specifying -<var class="var">option</var> <var class="var">value</var> in the
FFmpeg tools, or by setting the value explicitly in the device
<code class="code">AVFormatContext</code> options or using the <samp class="file">libavutil/opt.h</samp> API
for programmatic use.
</p>

<a name="Input-Devices"></a>
<h2 class="chapter">3 Input Devices<span class="pull-right"><a class="anchor hidden-xs" href="#Input-Devices" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Input-Devices" aria-hidden="true">TOC</a></span></h2>

<p>Input devices are configured elements in FFmpeg which enable accessing
the data coming from a multimedia device attached to your system.
</p>
<p>When you configure your FFmpeg build, all the supported input devices
are enabled by default. You can list all available ones using the
configure option &quot;&ndash;list-indevs&quot;.
</p>
<p>You can disable all the input devices using the configure option
&quot;&ndash;disable-indevs&quot;, and selectively enable an input device using the
option &quot;&ndash;enable-indev=<var class="var">INDEV</var>&quot;, or you can disable a particular
input device using the option &quot;&ndash;disable-indev=<var class="var">INDEV</var>&quot;.
</p>
<p>The option &quot;-devices&quot; of the ff* tools will display the list of
supported input devices.
</p>
<p>A description of the currently available input devices follows.
</p>
<a name="alsa"></a>
<h3 class="section">3.1 alsa<span class="pull-right"><a class="anchor hidden-xs" href="#alsa" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alsa" aria-hidden="true">TOC</a></span></h3>

<p>ALSA (Advanced Linux Sound Architecture) input device.
</p>
<p>To enable this input device during configuration you need libasound
installed on your system.
</p>
<p>This device allows capturing from an ALSA device. The name of the
device to capture has to be an ALSA card identifier.
</p>
<p>An ALSA identifier has the syntax:
</p><div class="example">
<pre class="example-preformatted">hw:<var class="var">CARD</var>[,<var class="var">DEV</var>[,<var class="var">SUBDEV</var>]]
</pre></div>

<p>where the <var class="var">DEV</var> and <var class="var">SUBDEV</var> components are optional.
</p>
<p>The three arguments (in order: <var class="var">CARD</var>,<var class="var">DEV</var>,<var class="var">SUBDEV</var>)
specify card number or identifier, device number and subdevice number
(-1 means any).
</p>
<p>To see the list of cards currently recognized by your system check the
files <samp class="file">/proc/asound/cards</samp> and <samp class="file">/proc/asound/devices</samp>.
</p>
<p>For example to capture with <code class="command">ffmpeg</code> from an ALSA device with
card id 0, you may run the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f alsa -i hw:0 alsaout.wav
</pre></div>

<p>For more information see:
<a class="url" href="http://www.alsa-project.org/alsa-doc/alsa-lib/pcm.html">http://www.alsa-project.org/alsa-doc/alsa-lib/pcm.html</a>
</p>
<a name="Options"></a>
<h4 class="subsection">3.1.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="android_005fcamera"></a>
<h3 class="section">3.2 android_camera<span class="pull-right"><a class="anchor hidden-xs" href="#android_005fcamera" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-android_005fcamera" aria-hidden="true">TOC</a></span></h3>

<p>Android camera input device.
</p>
<p>This input devices uses the Android Camera2 NDK API which is
available on devices with API level 24+. The availability of
android_camera is autodetected during configuration.
</p>
<p>This device allows capturing from all cameras on an Android device,
which are integrated into the Camera2 NDK API.
</p>
<p>The available cameras are enumerated internally and can be selected
with the <var class="var">camera_index</var> parameter. The input file string is
discarded.
</p>
<p>Generally the back facing camera has index 0 while the front facing
camera has index 1.
</p>
<a name="Options-1"></a>
<h4 class="subsection">3.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-1" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video size given as a string such as 640x480 or hd720.
Falls back to the first available configuration reported by
Android if requested video size is not available or by default.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the video framerate.
Falls back to the first available configuration reported by
Android if requested framerate is not available or by default (-1).
</p>
</dd>
<dt><samp class="option">camera_index</samp></dt>
<dd><p>Set the index of the camera to use. Default is 0.
</p>
</dd>
<dt><samp class="option">input_queue_size</samp></dt>
<dd><p>Set the maximum number of frames to buffer. Default is 5.
</p>
</dd>
</dl>

<a name="avfoundation"></a>
<h3 class="section">3.3 avfoundation<span class="pull-right"><a class="anchor hidden-xs" href="#avfoundation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avfoundation" aria-hidden="true">TOC</a></span></h3>

<p>AVFoundation input device.
</p>
<p>AVFoundation is the currently recommended framework by Apple for streamgrabbing on OSX &gt;= 10.7 as well as on iOS.
</p>
<p>The input filename has to be given in the following syntax:
</p><div class="example">
<pre class="example-preformatted">-i &quot;[[VIDEO]:[AUDIO]]&quot;
</pre></div>
<p>The first entry selects the video input while the latter selects the audio input.
The stream has to be specified by the device name or the device index as shown by the device list.
Alternatively, the video and/or audio input device can be chosen by index using the
<samp class="option">
    -video_device_index &lt;INDEX&gt;
</samp>
and/or
<samp class="option">
    -audio_device_index &lt;INDEX&gt;
</samp>
, overriding any
device name or index given in the input filename.
</p>
<p>All available devices can be enumerated by using <samp class="option">-list_devices true</samp>, listing
all device names and corresponding indices.
</p>
<p>There are two device name aliases:
</p><dl class="table">
<dt><code class="code">default</code></dt>
<dd><p>Select the AVFoundation default device of the corresponding type.
</p>
</dd>
<dt><code class="code">none</code></dt>
<dd><p>Do not record the corresponding media type.
This is equivalent to specifying an empty device name or index.
</p>
</dd>
</dl>

<a name="Options-2"></a>
<h4 class="subsection">3.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-2" aria-hidden="true">TOC</a></span></h4>

<p>AVFoundation supports the following options:
</p>
<dl class="table">
<dt><samp class="option">-list_devices &lt;TRUE|FALSE&gt;</samp></dt>
<dd><p>If set to true, a list of all available input devices is given showing all
device names and indices.
</p>
</dd>
<dt><samp class="option">-video_device_index &lt;INDEX&gt;</samp></dt>
<dd><p>Specify the video device by its index. Overrides anything given in the input filename.
</p>
</dd>
<dt><samp class="option">-audio_device_index &lt;INDEX&gt;</samp></dt>
<dd><p>Specify the audio device by its index. Overrides anything given in the input filename.
</p>
</dd>
<dt><samp class="option">-pixel_format &lt;FORMAT&gt;</samp></dt>
<dd><p>Request the video device to use a specific pixel format.
If the specified format is not supported, a list of available formats is given
and the first one in this list is used instead. Available pixel formats are:
<code class="code">monob, rgb555be, rgb555le, rgb565be, rgb565le, rgb24, bgr24, 0rgb, bgr0, 0bgr, rgb0,
 bgr48be, uyvy422, yuva444p, yuva444p16le, yuv444p, yuv422p16, yuv422p10, yuv444p10,
 yuv420p, nv12, yuyv422, gray</code>
</p>
</dd>
<dt><samp class="option">-framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default is <code class="code">ntsc</code>, corresponding to a
frame rate of <code class="code">30000/1001</code>.
</p>
</dd>
<dt><samp class="option">-video_size</samp></dt>
<dd><p>Set the video frame size.
</p>
</dd>
<dt><samp class="option">-capture_cursor</samp></dt>
<dd><p>Capture the mouse pointer. Default is 0.
</p>
</dd>
<dt><samp class="option">-capture_mouse_clicks</samp></dt>
<dd><p>Capture the screen mouse clicks. Default is 0.
</p>
</dd>
<dt><samp class="option">-capture_raw_data</samp></dt>
<dd><p>Capture the raw device data. Default is 0.
Using this option may result in receiving the underlying data delivered to the AVFoundation framework. E.g. for muxed devices that sends raw DV data to the framework (like tape-based camcorders), setting this option to false results in extracted video frames captured in the designated pixel format only. Setting this option to true results in receiving the raw DV stream untouched.
</p></dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">3.3.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Print the list of AVFoundation supported devices and exit:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f avfoundation -list_devices true -i &quot;&quot;
</pre></div>

</li><li>Record video from video device 0 and audio from audio device 0 into out.avi:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f avfoundation -i &quot;0:0&quot; out.avi
</pre></div>

</li><li>Record video from video device 2 and audio from audio device 1 into out.avi:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f avfoundation -video_device_index 2 -i &quot;:1&quot; out.avi
</pre></div>

</li><li>Record video from the system default video device using the pixel format bgr0 and do not record any audio into out.avi:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f avfoundation -pixel_format bgr0 -i &quot;default:none&quot; out.avi
</pre></div>

</li><li>Record raw DV data from a suitable input device and write the output into out.dv:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f avfoundation -capture_raw_data true -i &quot;zr100:none&quot; out.dv
</pre></div>


</li></ul>

<a name="decklink"></a>
<h3 class="section">3.4 decklink<span class="pull-right"><a class="anchor hidden-xs" href="#decklink" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-decklink" aria-hidden="true">TOC</a></span></h3>

<p>The decklink input device provides capture capabilities for Blackmagic
DeckLink devices.
</p>
<p>To enable this input device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate <code class="code">--extra-cflags</code>
and <code class="code">--extra-ldflags</code>.
On Windows, you need to run the IDL files through <code class="command">widl</code>.
</p>
<p>DeckLink is very picky about the formats it supports. Pixel format of the
input can be set with <samp class="option">raw_format</samp>.
Framerate and video size must be determined for your device with
<code class="command">-list_formats 1</code>. Audio sample rate is always 48 kHz and the number
of channels can be 2, 8 or 16. Note that all audio channels are bundled in one single
audio track.
</p>
<a name="Options-3"></a>
<h4 class="subsection">3.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-3" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">list_devices</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of devices and exit.
Defaults to <samp class="option">false</samp>. This option is deprecated, please use the
<code class="code">-sources</code> option of ffmpeg to list the available input devices.
</p>
</dd>
<dt><samp class="option">list_formats</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of supported formats and exit.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
<dt><samp class="option">format_code &lt;FourCC&gt;</samp></dt>
<dd><p>This sets the input video format to the format given by the FourCC. To see
the supported values of your device(s) use <samp class="option">list_formats</samp>.
Note that there is a FourCC <samp class="option">'pal '</samp> that can also be used
as <samp class="option">pal</samp> (3 letters).
Default behavior is autodetection of the input video format, if the hardware
supports it.
</p>
</dd>
<dt><samp class="option">raw_format</samp></dt>
<dd><p>Set the pixel format of the captured video.
Available values are:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd>
<p>This is the default which means 8-bit YUV 422 or 8-bit ARGB if format
autodetection is used, 8-bit YUV 422 otherwise.
</p>
</dd>
<dt>&lsquo;<samp class="samp">uyvy422</samp>&rsquo;</dt>
<dd>
<p>8-bit YUV 422.
</p>
</dd>
<dt>&lsquo;<samp class="samp">yuv422p10</samp>&rsquo;</dt>
<dd>
<p>10-bit YUV 422.
</p>
</dd>
<dt>&lsquo;<samp class="samp">argb</samp>&rsquo;</dt>
<dd>
<p>8-bit RGB.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bgra</samp>&rsquo;</dt>
<dd>
<p>8-bit RGB.
</p>
</dd>
<dt>&lsquo;<samp class="samp">rgb10</samp>&rsquo;</dt>
<dd>
<p>10-bit RGB.
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">teletext_lines</samp></dt>
<dd><p>If set to nonzero, an additional teletext stream will be captured from the
vertical ancillary data. Both SD PAL (576i) and HD (1080i or 1080p)
sources are supported. In case of HD sources, OP47 packets are decoded.
</p>
<p>This option is a bitmask of the SD PAL VBI lines captured, specifically lines 6
to 22, and lines 318 to 335. Line 6 is the LSB in the mask. Selected lines
which do not contain teletext information will be ignored. You can use the
special <samp class="option">all</samp> constant to select all possible lines, or
<samp class="option">standard</samp> to skip lines 6, 318 and 319, which are not compatible with
all receivers.
</p>
<p>For SD sources, ffmpeg needs to be compiled with <code class="code">--enable-libzvbi</code>. For
HD sources, on older (pre-4K) DeckLink card models you have to capture in 10
bit mode.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Defines number of audio channels to capture. Must be &lsquo;<samp class="samp">2</samp>&rsquo;, &lsquo;<samp class="samp">8</samp>&rsquo; or &lsquo;<samp class="samp">16</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">2</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">duplex_mode</samp></dt>
<dd><p>Sets the decklink device duplex/profile mode. Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">half</samp>&rsquo;, &lsquo;<samp class="samp">full</samp>&rsquo;,
&lsquo;<samp class="samp">one_sub_device_full</samp>&rsquo;, &lsquo;<samp class="samp">one_sub_device_half</samp>&rsquo;, &lsquo;<samp class="samp">two_sub_device_full</samp>&rsquo;,
&lsquo;<samp class="samp">four_sub_device_half</samp>&rsquo;
Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
<p>Note: DeckLink SDK 11.0 have replaced the duplex property by a profile property.
For the DeckLink Duo 2 and DeckLink Quad 2, a profile is shared between any 2
sub-devices that utilize the same connectors. For the DeckLink 8K Pro, a profile
is shared between all 4 sub-devices. So DeckLink 8K Pro support four profiles.
</p>
<p>Valid profile modes for DeckLink 8K Pro(with DeckLink SDK &gt;= 11.0):
&lsquo;<samp class="samp">one_sub_device_full</samp>&rsquo;, &lsquo;<samp class="samp">one_sub_device_half</samp>&rsquo;, &lsquo;<samp class="samp">two_sub_device_full</samp>&rsquo;,
&lsquo;<samp class="samp">four_sub_device_half</samp>&rsquo;
</p>
<p>Valid profile modes for DeckLink Quad 2 and DeckLink Duo 2:
&lsquo;<samp class="samp">half</samp>&rsquo;, &lsquo;<samp class="samp">full</samp>&rsquo;
</p>
</dd>
<dt><samp class="option">timecode_format</samp></dt>
<dd><p>Timecode type to include in the frame and video stream metadata. Must be
&lsquo;<samp class="samp">none</samp>&rsquo;, &lsquo;<samp class="samp">rp188vitc</samp>&rsquo;, &lsquo;<samp class="samp">rp188vitc2</samp>&rsquo;, &lsquo;<samp class="samp">rp188ltc</samp>&rsquo;,
&lsquo;<samp class="samp">rp188hfr</samp>&rsquo;, &lsquo;<samp class="samp">rp188any</samp>&rsquo;, &lsquo;<samp class="samp">vitc</samp>&rsquo;, &lsquo;<samp class="samp">vitc2</samp>&rsquo;, or &lsquo;<samp class="samp">serial</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">none</samp>&rsquo; (not included).
</p>
<p>In order to properly support 50/60 fps timecodes, the ordering of the queried
timecode types for &lsquo;<samp class="samp">rp188any</samp>&rsquo; is HFR, VITC1, VITC2 and LTC for &gt;30 fps
content. Note that this is slightly different to the ordering used by the
DeckLink API, which is HFR, VITC1, LTC, VITC2.
</p>
</dd>
<dt><samp class="option">video_input</samp></dt>
<dd><p>Sets the video input source. Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">sdi</samp>&rsquo;, &lsquo;<samp class="samp">hdmi</samp>&rsquo;,
&lsquo;<samp class="samp">optical_sdi</samp>&rsquo;, &lsquo;<samp class="samp">component</samp>&rsquo;, &lsquo;<samp class="samp">composite</samp>&rsquo; or &lsquo;<samp class="samp">s_video</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">audio_input</samp></dt>
<dd><p>Sets the audio input source. Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">embedded</samp>&rsquo;,
&lsquo;<samp class="samp">aes_ebu</samp>&rsquo;, &lsquo;<samp class="samp">analog</samp>&rsquo;, &lsquo;<samp class="samp">analog_xlr</samp>&rsquo;, &lsquo;<samp class="samp">analog_rca</samp>&rsquo; or
&lsquo;<samp class="samp">microphone</samp>&rsquo;. Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">video_pts</samp></dt>
<dd><p>Sets the video packet timestamp source. Must be &lsquo;<samp class="samp">video</samp>&rsquo;, &lsquo;<samp class="samp">audio</samp>&rsquo;,
&lsquo;<samp class="samp">reference</samp>&rsquo;, &lsquo;<samp class="samp">wallclock</samp>&rsquo; or &lsquo;<samp class="samp">abs_wallclock</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">video</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">audio_pts</samp></dt>
<dd><p>Sets the audio packet timestamp source. Must be &lsquo;<samp class="samp">video</samp>&rsquo;, &lsquo;<samp class="samp">audio</samp>&rsquo;,
&lsquo;<samp class="samp">reference</samp>&rsquo;, &lsquo;<samp class="samp">wallclock</samp>&rsquo; or &lsquo;<samp class="samp">abs_wallclock</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">audio</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">draw_bars</samp></dt>
<dd><p>If set to &lsquo;<samp class="samp">true</samp>&rsquo;, color bars are drawn in the event of a signal loss.
Defaults to &lsquo;<samp class="samp">true</samp>&rsquo;.
This option is deprecated, please use the <code class="code">signal_loss_action</code> option.
</p>
</dd>
<dt><samp class="option">signal_loss_action</samp></dt>
<dd><p>Sets the action to take in the event of a signal loss. Accepts one of the
following values:
</p>
<dl class="table">
<dt><samp class="option">1, none</samp></dt>
<dd><p>Do nothing on signal loss. This usually results in black frames.
</p></dd>
<dt><samp class="option">2, bars</samp></dt>
<dd><p>Draw color bars on signal loss. Only supported for 8-bit input signals.
</p></dd>
<dt><samp class="option">3, repeat</samp></dt>
<dd><p>Repeat the last video frame on signal loss.
</p></dd>
</dl>

<p>Defaults to &lsquo;<samp class="samp">bars</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">queue_size</samp></dt>
<dd><p>Sets maximum input buffer size in bytes. If the buffering reaches this value,
incoming frames will be dropped.
Defaults to &lsquo;<samp class="samp">1073741824</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">audio_depth</samp></dt>
<dd><p>Sets the audio sample bit depth. Must be &lsquo;<samp class="samp">16</samp>&rsquo; or &lsquo;<samp class="samp">32</samp>&rsquo;.
Defaults to &lsquo;<samp class="samp">16</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">decklink_copyts</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, timestamps are forwarded as they are without removing
the initial offset.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
<dt><samp class="option">timestamp_align</samp></dt>
<dd><p>Capture start time alignment in seconds. If set to nonzero, input frames are
dropped till the system timestamp aligns with configured value.
Alignment difference of up to one frame duration is tolerated.
This is useful for maintaining input synchronization across N different
hardware devices deployed for &rsquo;N-way&rsquo; redundancy. The system time of different
hardware devices should be synchronized with protocols such as NTP or PTP,
before using this option.
Note that this method is not foolproof. In some border cases input
synchronization may not happen due to thread scheduling jitters in the OS.
Either sync could go wrong by 1 frame or in a rarer case
<samp class="option">timestamp_align</samp> seconds.
Defaults to &lsquo;<samp class="samp">0</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">wait_for_tc (<em class="emph">bool</em>)</samp></dt>
<dd><p>Drop frames till a frame with timecode is received. Sometimes serial timecode
isn&rsquo;t received with the first input frame. If that happens, the stored stream
timecode will be inaccurate. If this option is set to <samp class="option">true</samp>, input frames
are dropped till a frame with timecode is received.
Option <var class="var">timecode_format</var> must be specified.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
<dt><samp class="option">enable_klv(<em class="emph">bool</em>)</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, extracts KLV data from VANC and outputs KLV packets.
KLV VANC packets are joined based on MID and PSC fields and aggregated into
one KLV packet.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
</dl>

<a name="Examples-1"></a>
<h4 class="subsection">3.4.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>List input devices:
<div class="example">
<pre class="example-preformatted">ffmpeg -sources decklink
</pre></div>

</li><li>List supported formats:
<div class="example">
<pre class="example-preformatted">ffmpeg -f decklink -list_formats 1 -i 'Intensity Pro'
</pre></div>

</li><li>Capture video clip at 1080i50:
<div class="example">
<pre class="example-preformatted">ffmpeg -format_code Hi50 -f decklink -i 'Intensity Pro' -c:a copy -c:v copy output.avi
</pre></div>

</li><li>Capture video clip at 1080i50 10 bit:
<div class="example">
<pre class="example-preformatted">ffmpeg -raw_format yuv422p10 -format_code Hi50 -f decklink -i 'UltraStudio Mini Recorder' -c:a copy -c:v copy output.avi
</pre></div>

</li><li>Capture video clip at 1080i50 with 16 audio channels:
<div class="example">
<pre class="example-preformatted">ffmpeg -channels 16 -format_code Hi50 -f decklink -i 'UltraStudio Mini Recorder' -c:a copy -c:v copy output.avi
</pre></div>

</li></ul>

<a name="dshow"></a>
<h3 class="section">3.5 dshow<span class="pull-right"><a class="anchor hidden-xs" href="#dshow" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dshow" aria-hidden="true">TOC</a></span></h3>

<p>Windows DirectShow input device.
</p>
<p>DirectShow support is enabled when FFmpeg is built with the mingw-w64 project.
Currently only audio and video devices are supported.
</p>
<p>Multiple devices may be opened as separate inputs, but they may also be
opened on the same input, which should improve synchronism between them.
</p>
<p>The input name should be in the format:
</p>
<div class="example">
<pre class="example-preformatted"><var class="var">TYPE</var>=<var class="var">NAME</var>[:<var class="var">TYPE</var>=<var class="var">NAME</var>]
</pre></div>

<p>where <var class="var">TYPE</var> can be either <var class="var">audio</var> or <var class="var">video</var>,
and <var class="var">NAME</var> is the device&rsquo;s name or alternative name..
</p>
<a name="Options-4"></a>
<h4 class="subsection">3.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-4" aria-hidden="true">TOC</a></span></h4>

<p>If no options are specified, the device&rsquo;s defaults are used.
If the device does not support the requested options, it will
fail to open.
</p>
<dl class="table">
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video size in the captured video.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the frame rate in the captured video.
</p>
</dd>
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate (in Hz) of the captured audio.
</p>
</dd>
<dt><samp class="option">sample_size</samp></dt>
<dd><p>Set the sample size (in bits) of the captured audio.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels in the captured audio.
</p>
</dd>
<dt><samp class="option">list_devices</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of devices and exit.
</p>
</dd>
<dt><samp class="option">list_options</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of selected device&rsquo;s options
and exit.
</p>
</dd>
<dt><samp class="option">video_device_number</samp></dt>
<dd><p>Set video device number for devices with the same name (starts at 0,
defaults to 0).
</p>
</dd>
<dt><samp class="option">audio_device_number</samp></dt>
<dd><p>Set audio device number for devices with the same name (starts at 0,
defaults to 0).
</p>
</dd>
<dt><samp class="option">pixel_format</samp></dt>
<dd><p>Select pixel format to be used by DirectShow. This may only be set when
the video codec is not set or set to rawvideo.
</p>
</dd>
<dt><samp class="option">audio_buffer_size</samp></dt>
<dd><p>Set audio device buffer size in milliseconds (which can directly
impact latency, depending on the device).
Defaults to using the audio device&rsquo;s
default buffer size (typically some multiple of 500ms).
Setting this value too low can degrade performance.
See also
<a class="url" href="http://msdn.microsoft.com/en-us/library/windows/desktop/dd377582(v=vs.85).aspx">http://msdn.microsoft.com/en-us/library/windows/desktop/dd377582(v=vs.85).aspx</a>
</p>
</dd>
<dt><samp class="option">video_pin_name</samp></dt>
<dd><p>Select video capture pin to use by name or alternative name.
</p>
</dd>
<dt><samp class="option">audio_pin_name</samp></dt>
<dd><p>Select audio capture pin to use by name or alternative name.
</p>
</dd>
<dt><samp class="option">crossbar_video_input_pin_number</samp></dt>
<dd><p>Select video input pin number for crossbar device. This will be
routed to the crossbar device&rsquo;s Video Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.
</p>
</dd>
<dt><samp class="option">crossbar_audio_input_pin_number</samp></dt>
<dd><p>Select audio input pin number for crossbar device. This will be
routed to the crossbar device&rsquo;s Audio Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.
</p>
</dd>
<dt><samp class="option">show_video_device_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display dialog
to the end user, allowing them to change video filter properties
and configurations manually.
Note that for crossbar devices, adjusting values in this dialog
may be needed at times to toggle between PAL (25 fps) and NTSC (29.97)
input frame rates, sizes, interlacing, etc.  Changing these values can
enable different scan rates/frame rates and avoiding green bars at
the bottom, flickering scan lines, etc.
Note that with some devices, changing these properties can also affect future
invocations (sets new defaults) until system reboot occurs.
</p>
</dd>
<dt><samp class="option">show_audio_device_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display dialog
to the end user, allowing them to change audio filter properties
and configurations manually.
</p>
</dd>
<dt><samp class="option">show_video_crossbar_connection_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens a video device.
</p>
</dd>
<dt><samp class="option">show_audio_crossbar_connection_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens an audio device.
</p>
</dd>
<dt><samp class="option">show_analog_tv_tuner_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV channels and frequencies.
</p>
</dd>
<dt><samp class="option">show_analog_tv_tuner_audio_dialog</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV audio (like mono vs. stereo, Language A,B or C).
</p>
</dd>
<dt><samp class="option">audio_device_load</samp></dt>
<dd><p>Load an audio capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this an audio capture source has to be specified, but it can
be anything even fake one.
</p>
</dd>
<dt><samp class="option">audio_device_save</samp></dt>
<dd><p>Save the currently used audio capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.
</p>
</dd>
<dt><samp class="option">video_device_load</samp></dt>
<dd><p>Load a video capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this a video capture source has to be specified, but it can
be anything even fake one.
</p>
</dd>
<dt><samp class="option">video_device_save</samp></dt>
<dd><p>Save the currently used video capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.
</p>
</dd>
<dt><samp class="option">use_video_device_timestamps</samp></dt>
<dd><p>If set to <samp class="option">false</samp>, the timestamp for video frames will be
derived from the wallclock instead of the timestamp provided by
the capture device. This allows working around devices that
provide unreliable timestamps.
</p>
</dd>
</dl>

<a name="Examples-2"></a>
<h4 class="subsection">3.5.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-2" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Print the list of DirectShow supported devices and exit:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -list_devices true -f dshow -i dummy
</pre></div>

</li><li>Open video device <var class="var">Camera</var>:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f dshow -i video=&quot;Camera&quot;
</pre></div>

</li><li>Open second video device with name <var class="var">Camera</var>:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f dshow -video_device_number 1 -i video=&quot;Camera&quot;
</pre></div>

</li><li>Open video device <var class="var">Camera</var> and audio device <var class="var">Microphone</var>:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f dshow -i video=&quot;Camera&quot;:audio=&quot;Microphone&quot;
</pre></div>

</li><li>Print the list of supported options in selected device and exit:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -list_options true -f dshow -i video=&quot;Camera&quot;
</pre></div>

</li><li>Specify pin names to capture by name or alternative name, specify alternative device name:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f dshow -audio_pin_name &quot;Audio Out&quot; -video_pin_name 2 -i video=video=&quot;@device_pnp_\\?\pci#ven_1a0a&amp;dev_6200&amp;subsys_62021461&amp;rev_01#4&amp;e2c7dd6&amp;0&amp;00e1#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\{ca465100-deb0-4d59-818f-8c477184adf6}&quot;:audio=&quot;Microphone&quot;
</pre></div>

</li><li>Configure a crossbar device, specifying crossbar pins, allow user to adjust video capture properties at startup:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f dshow -show_video_device_dialog true -crossbar_video_input_pin_number 0
     -crossbar_audio_input_pin_number 3 -i video=&quot;AVerMedia BDA Analog Capture&quot;:audio=&quot;AVerMedia BDA Analog Capture&quot;
</pre></div>

</li></ul>

<a name="fbdev"></a>
<h3 class="section">3.6 fbdev<span class="pull-right"><a class="anchor hidden-xs" href="#fbdev" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fbdev" aria-hidden="true">TOC</a></span></h3>

<p>Linux framebuffer input device.
</p>
<p>The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
<samp class="file">/dev/fb0</samp>.
</p>
<p>For more detailed information read the file
Documentation/fb/framebuffer.txt included in the Linux source tree.
</p>
<p>See also <a class="url" href="http://linux-fbdev.sourceforge.net/">http://linux-fbdev.sourceforge.net/</a>, and fbset(1).
</p>
<p>To record from the framebuffer device <samp class="file">/dev/fb0</samp> with
<code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f fbdev -framerate 10 -i /dev/fb0 out.avi
</pre></div>

<p>You can take a single screenshot image with the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f fbdev -framerate 1 -i /dev/fb0 -frames:v 1 screenshot.jpeg
</pre></div>

<a name="Options-5"></a>
<h4 class="subsection">3.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-5" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the frame rate. Default is 25.
</p>
</dd>
</dl>

<a name="gdigrab"></a>
<h3 class="section">3.7 gdigrab<span class="pull-right"><a class="anchor hidden-xs" href="#gdigrab" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gdigrab" aria-hidden="true">TOC</a></span></h3>

<p>Win32 GDI-based screen capture device.
</p>
<p>This device allows you to capture a region of the display on Windows.
</p>
<p>Amongst options for the imput filenames are such elements as:
</p><div class="example">
<pre class="example-preformatted">desktop
</pre></div>
<p>or
</p><div class="example">
<pre class="example-preformatted">title=<var class="var">window_title</var>
</pre></div>
<p>or
</p><div class="example">
<pre class="example-preformatted">hwnd=<var class="var">window_hwnd</var>
</pre></div>

<p>The first option will capture the entire desktop, or a fixed region of the
desktop. The second and third options will instead capture the contents of a single
window, regardless of its position on the screen.
</p>
<p>For example, to grab the entire desktop using <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f gdigrab -framerate 6 -i desktop out.mpg
</pre></div>

<p>Grab a 640x480 region at position <code class="code">10,20</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f gdigrab -framerate 6 -offset_x 10 -offset_y 20 -video_size vga -i desktop out.mpg
</pre></div>

<p>Grab the contents of the window named &quot;Calculator&quot;
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f gdigrab -framerate 6 -i title=Calculator out.mpg
</pre></div>

<a name="Options-6"></a>
<h4 class="subsection">3.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-6" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">draw_mouse</samp></dt>
<dd><p>Specify whether to draw the mouse pointer. Use the value <code class="code">0</code> to
not draw the pointer. Default value is <code class="code">1</code>.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code class="code">ntsc</code>,
corresponding to a frame rate of <code class="code">30000/1001</code>.
</p>
</dd>
<dt><samp class="option">show_region</samp></dt>
<dd><p>Show grabbed region on screen.
</p>
<p>If <var class="var">show_region</var> is specified with <code class="code">1</code>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.
</p>
<p>Note that <var class="var">show_region</var> is incompatible with grabbing the contents
of a single window.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f gdigrab -show_region 1 -framerate 6 -video_size cif -offset_x 10 -offset_y 20 -i desktop out.mpg
</pre></div>

</dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video frame size. The default is to capture the full screen if <samp class="file">desktop</samp> is selected, or the full window size if <samp class="file">title=<var class="var">window_title</var></samp> is selected.
</p>
</dd>
<dt><samp class="option">offset_x</samp></dt>
<dd><p>When capturing a region with <var class="var">video_size</var>, set the distance from the left edge of the screen or desktop.
</p>
<p>Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned to the left of your primary monitor, you will need to use a negative <var class="var">offset_x</var> value to move the region to that monitor.
</p>
</dd>
<dt><samp class="option">offset_y</samp></dt>
<dd><p>When capturing a region with <var class="var">video_size</var>, set the distance from the top edge of the screen or desktop.
</p>
<p>Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned above your primary monitor, you will need to use a negative <var class="var">offset_y</var> value to move the region to that monitor.
</p>
</dd>
</dl>

<a name="iec61883"></a>
<h3 class="section">3.8 iec61883<span class="pull-right"><a class="anchor hidden-xs" href="#iec61883" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-iec61883" aria-hidden="true">TOC</a></span></h3>

<p>FireWire DV/HDV input device using libiec61883.
</p>
<p>To enable this input device, you need libiec61883, libraw1394 and
libavc1394 installed on your system. Use the configure option
<code class="code">--enable-libiec61883</code> to compile with the device enabled.
</p>
<p>The iec61883 capture device supports capturing from a video device
connected via IEEE1394 (FireWire), using libiec61883 and the new Linux
FireWire stack (juju). This is the default DV/HDV input method in Linux
Kernel 2.6.37 and later, since the old FireWire stack was removed.
</p>
<p>Specify the FireWire port to be used as input file, or &quot;auto&quot;
to choose the first port connected.
</p>
<a name="Options-7"></a>
<h4 class="subsection">3.8.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-7" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">dvtype</samp></dt>
<dd><p>Override autodetection of DV/HDV. This should only be used if auto
detection does not work, or if usage of a different device type
should be prohibited. Treating a DV device as HDV (or vice versa) will
not work and result in undefined behavior.
The values <samp class="option">auto</samp>, <samp class="option">dv</samp> and <samp class="option">hdv</samp> are supported.
</p>
</dd>
<dt><samp class="option">dvbuffer</samp></dt>
<dd><p>Set maximum size of buffer for incoming data, in frames. For DV, this
is an exact value. For HDV, it is not frame exact, since HDV does
not have a fixed frame size.
</p>
</dd>
<dt><samp class="option">dvguid</samp></dt>
<dd><p>Select the capture device by specifying its GUID. Capturing will only
be performed from the specified device and fails if no device with the
given GUID is found. This is useful to select the input if multiple
devices are connected at the same time.
Look at /sys/bus/firewire/devices to find out the GUIDs.
</p>
</dd>
</dl>

<a name="Examples-3"></a>
<h4 class="subsection">3.8.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-3" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Grab and show the input of a FireWire DV/HDV device.
<div class="example">
<pre class="example-preformatted">ffplay -f iec61883 -i auto
</pre></div>

</li><li>Grab and record the input of a FireWire DV/HDV device,
using a packet buffer of 100000 packets if the source is HDV.
<div class="example">
<pre class="example-preformatted">ffmpeg -f iec61883 -i auto -dvbuffer 100000 out.mpg
</pre></div>

</li></ul>

<a name="jack"></a>
<h3 class="section">3.9 jack<span class="pull-right"><a class="anchor hidden-xs" href="#jack" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-jack" aria-hidden="true">TOC</a></span></h3>

<p>JACK input device.
</p>
<p>To enable this input device during configuration you need libjack
installed on your system.
</p>
<p>A JACK input device creates one or more JACK writable clients, one for
each audio channel, with name <var class="var">client_name</var>:input_<var class="var">N</var>, where
<var class="var">client_name</var> is the name provided by the application, and <var class="var">N</var>
is a number which identifies the channel.
Each writable client will send the acquired data to the FFmpeg input
device.
</p>
<p>Once you have created one or more JACK readable clients, you need to
connect them to one or more JACK writable clients.
</p>
<p>To connect or disconnect JACK clients you can use the <code class="command">jack_connect</code>
and <code class="command">jack_disconnect</code> programs, or do it through a graphical interface,
for example with <code class="command">qjackctl</code>.
</p>
<p>To list the JACK clients and their properties you can invoke the command
<code class="command">jack_lsp</code>.
</p>
<p>Follows an example which shows how to capture a JACK readable client
with <code class="command">ffmpeg</code>.
</p><div class="example">
<pre class="example-preformatted"># Create a JACK writable client with name &quot;ffmpeg&quot;.
$ ffmpeg -f jack -i ffmpeg -y out.wav

# Start the sample jack_metro readable client.
$ jack_metro -b 120 -d 0.2 -f 4000

# List the current JACK clients.
$ jack_lsp -c
system:capture_1
system:capture_2
system:playback_1
system:playback_2
ffmpeg:input_1
metro:120_bpm

# Connect metro to the ffmpeg writable client.
$ jack_connect metro:120_bpm ffmpeg:input_1
</pre></div>

<p>For more information read:
<a class="url" href="http://jackaudio.org/">http://jackaudio.org/</a>
</p>
<a name="Options-8"></a>
<h4 class="subsection">3.9.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-8" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="kmsgrab"></a>
<h3 class="section">3.10 kmsgrab<span class="pull-right"><a class="anchor hidden-xs" href="#kmsgrab" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-kmsgrab" aria-hidden="true">TOC</a></span></h3>

<p>KMS video input device.
</p>
<p>Captures the KMS scanout framebuffer associated with a specified CRTC or plane as a
DRM object that can be passed to other hardware functions.
</p>
<p>Requires either DRM master or CAP_SYS_ADMIN to run.
</p>
<p>If you don&rsquo;t understand what all of that means, you probably don&rsquo;t want this.  Look at
<samp class="option">x11grab</samp> instead.
</p>
<a name="Options-9"></a>
<h4 class="subsection">3.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-9" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">device</samp></dt>
<dd><p>DRM device to capture on.  Defaults to <samp class="option">/dev/dri/card0</samp>.
</p>
</dd>
<dt><samp class="option">format</samp></dt>
<dd><p>Pixel format of the framebuffer.  This can be autodetected if you are running Linux 5.7
or later, but needs to be provided for earlier versions.  Defaults to <samp class="option">bgr0</samp>,
which is the most common format used by the Linux console and Xorg X server.
</p>
</dd>
<dt><samp class="option">format_modifier</samp></dt>
<dd><p>Format modifier to signal on output frames.  This is necessary to import correctly into
some APIs.  It can be autodetected if you are running Linux 5.7 or later, but will need
to be provided explicitly when needed in earlier versions.  See the libdrm documentation
for possible values.
</p>
</dd>
<dt><samp class="option">crtc_id</samp></dt>
<dd><p>KMS CRTC ID to define the capture source.  The first active plane on the given CRTC
will be used.
</p>
</dd>
<dt><samp class="option">plane_id</samp></dt>
<dd><p>KMS plane ID to define the capture source.  Defaults to the first active plane found if
neither <samp class="option">crtc_id</samp> nor <samp class="option">plane_id</samp> are specified.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Framerate to capture at.  This is not synchronised to any page flipping or framebuffer
changes - it just defines the interval at which the framebuffer is sampled.  Sampling
faster than the framebuffer update rate will generate independent frames with the same
content.  Defaults to <code class="code">30</code>.
</p>
</dd>
</dl>

<a name="Examples-4"></a>
<h4 class="subsection">3.10.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-4" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Capture from the first active plane, download the result to normal frames and encode.
This will only work if the framebuffer is both linear and mappable - if not, the result
may be scrambled or fail to download.
<div class="example">
<pre class="example-preformatted">ffmpeg -f kmsgrab -i - -vf 'hwdownload,format=bgr0' output.mp4
</pre></div>

</li><li>Capture from CRTC ID 42 at 60fps, map the result to VAAPI, convert to NV12 and encode as H.264.
<div class="example">
<pre class="example-preformatted">ffmpeg -crtc_id 42 -framerate 60 -f kmsgrab -i - -vf 'hwmap=derive_device=vaapi,scale_vaapi=w=1920:h=1080:format=nv12' -c:v h264_vaapi output.mp4
</pre></div>

</li><li>To capture only part of a plane the output can be cropped - this can be used to capture
a single window, as long as it has a known absolute position and size.  For example, to
capture and encode the middle quarter of a 1920x1080 plane:
<div class="example">
<pre class="example-preformatted">ffmpeg -f kmsgrab -i - -vf 'hwmap=derive_device=vaapi,crop=960:540:480:270,scale_vaapi=960:540:nv12' -c:v h264_vaapi output.mp4
</pre></div>

</li></ul>

<a name="lavfi"></a>
<h3 class="section">3.11 lavfi<span class="pull-right"><a class="anchor hidden-xs" href="#lavfi" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-lavfi" aria-hidden="true">TOC</a></span></h3>

<p>Libavfilter input virtual device.
</p>
<p>This input device reads data from the open output pads of a libavfilter
filtergraph.
</p>
<p>For each filtergraph open output, the input device will create a
corresponding stream which is mapped to the generated output.
The filtergraph is specified through the option <samp class="option">graph</samp>.
</p>
<a name="Options-10"></a>
<h4 class="subsection">3.11.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-10" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">graph</samp></dt>
<dd><p>Specify the filtergraph to use as input. Each video open output must be
labelled by a unique string of the form &quot;out<var class="var">N</var>&quot;, where <var class="var">N</var> is a
number starting from 0 corresponding to the mapped input stream
generated by the device.
The first unlabelled output is automatically assigned to the &quot;out0&quot;
label, but all the others need to be specified explicitly.
</p>
<p>The suffix &quot;+subcc&quot; can be appended to the output label to create an extra
stream with the closed captions packets attached to that output
(experimental; only for EIA-608 / CEA-708 for now).
The subcc streams are created after all the normal streams, in the order of
the corresponding stream.
For example, if there is &quot;out19+subcc&quot;, &quot;out7+subcc&quot; and up to &quot;out42&quot;, the
stream #43 is subcc for stream #7 and stream #44 is subcc for stream #19.
</p>
<p>If not specified defaults to the filename specified for the input
device.
</p>
</dd>
<dt><samp class="option">graph_file</samp></dt>
<dd><p>Set the filename of the filtergraph to be read and sent to the other
filters. Syntax of the filtergraph is the same as the one specified by
the option <var class="var">graph</var>.
</p>
</dd>
<dt><samp class="option">dumpgraph</samp></dt>
<dd><p>Dump graph to stderr.
</p>
</dd>
</dl>

<a name="Examples-5"></a>
<h4 class="subsection">3.11.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-5" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Create a color video stream and play it back with <code class="command">ffplay</code>:
<div class="example">
<pre class="example-preformatted">ffplay -f lavfi -graph &quot;color=c=pink [out0]&quot; dummy
</pre></div>

</li><li>As the previous example, but use filename for specifying the graph
description, and omit the &quot;out0&quot; label:
<div class="example">
<pre class="example-preformatted">ffplay -f lavfi color=c=pink
</pre></div>

</li><li>Create three different video test filtered sources and play them:
<div class="example">
<pre class="example-preformatted">ffplay -f lavfi -graph &quot;testsrc [out0]; testsrc,hflip [out1]; testsrc,negate [out2]&quot; test3
</pre></div>

</li><li>Read an audio stream from a file using the amovie source and play it
back with <code class="command">ffplay</code>:
<div class="example">
<pre class="example-preformatted">ffplay -f lavfi &quot;amovie=test.wav&quot;
</pre></div>

</li><li>Read an audio stream and a video stream and play it back with
<code class="command">ffplay</code>:
<div class="example">
<pre class="example-preformatted">ffplay -f lavfi &quot;movie=test.avi[out0];amovie=test.wav[out1]&quot;
</pre></div>

</li><li>Dump decoded frames to images and Closed Captions to an RCWT backup:
<div class="example">
<pre class="example-preformatted">ffmpeg -f lavfi -i &quot;movie=test.ts[out0+subcc]&quot; -map v frame%08d.png -map s -c copy -f rcwt subcc.bin
</pre></div>

</li></ul>

<a name="libcdio"></a>
<h3 class="section">3.12 libcdio<span class="pull-right"><a class="anchor hidden-xs" href="#libcdio" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libcdio" aria-hidden="true">TOC</a></span></h3>

<p>Audio-CD input device based on libcdio.
</p>
<p>To enable this input device during configuration you need libcdio
installed on your system. It requires the configure option
<code class="code">--enable-libcdio</code>.
</p>
<p>This device allows playing and grabbing from an Audio-CD.
</p>
<p>For example to copy with <code class="command">ffmpeg</code> the entire Audio-CD in <samp class="file">/dev/sr0</samp>,
you may run the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f libcdio -i /dev/sr0 cd.wav
</pre></div>

<a name="Options-11"></a>
<h4 class="subsection">3.12.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-11" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">speed</samp></dt>
<dd><p>Set drive reading speed. Default value is 0.
</p>
<p>The speed is specified CD-ROM speed units. The speed is set through
the libcdio <code class="code">cdio_cddap_speed_set</code> function. On many CD-ROM
drives, specifying a value too large will result in using the fastest
speed.
</p>
</dd>
<dt><samp class="option">paranoia_mode</samp></dt>
<dd><p>Set paranoia recovery mode flags. It accepts one of the following values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">disable</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">verify</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">overlap</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">neverskip</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">full</samp>&rsquo;</dt>
</dl>

<p>Default value is &lsquo;<samp class="samp">disable</samp>&rsquo;.
</p>
<p>For more information about the available recovery modes, consult the
paranoia project documentation.
</p></dd>
</dl>

<a name="libdc1394"></a>
<h3 class="section">3.13 libdc1394<span class="pull-right"><a class="anchor hidden-xs" href="#libdc1394" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libdc1394" aria-hidden="true">TOC</a></span></h3>

<p>IIDC1394 input device, based on libdc1394 and libraw1394.
</p>
<p>Requires the configure option <code class="code">--enable-libdc1394</code>.
</p>
<a name="Options-12"></a>
<h4 class="subsection">3.13.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-12" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the frame rate. Default is <code class="code">ntsc</code>, corresponding to a frame
rate of <code class="code">30000/1001</code>.
</p>
</dd>
<dt><samp class="option">pixel_format</samp></dt>
<dd><p>Select the pixel format. Default is <code class="code">uyvy422</code>.
</p>
</dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video size given as a string such as <code class="code">640x480</code> or <code class="code">hd720</code>.
Default is <code class="code">qvga</code>.
</p></dd>
</dl>

<a name="openal"></a>
<h3 class="section">3.14 openal<span class="pull-right"><a class="anchor hidden-xs" href="#openal" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-openal" aria-hidden="true">TOC</a></span></h3>

<p>The OpenAL input device provides audio capture on all systems with a
working OpenAL 1.1 implementation.
</p>
<p>To enable this input device during configuration, you need OpenAL
headers and libraries installed on your system, and need to configure
FFmpeg with <code class="code">--enable-openal</code>.
</p>
<p>OpenAL headers and libraries should be provided as part of your OpenAL
implementation, or as an additional download (an SDK). Depending on your
installation you may need to specify additional flags via the
<code class="code">--extra-cflags</code> and <code class="code">--extra-ldflags</code> for allowing the build
system to locate the OpenAL headers and libraries.
</p>
<p>An incomplete list of OpenAL implementations follows:
</p>
<dl class="table">
<dt><strong class="strong">Creative</strong></dt>
<dd><p>The official Windows implementation, providing hardware acceleration
with supported devices and software fallback.
See <a class="url" href="http://openal.org/">http://openal.org/</a>.
</p></dd>
<dt><strong class="strong">OpenAL Soft</strong></dt>
<dd><p>Portable, open source (LGPL) software implementation. Includes
backends for the most common sound APIs on the Windows, Linux,
Solaris, and BSD operating systems.
See <a class="url" href="http://kcat.strangesoft.net/openal.html">http://kcat.strangesoft.net/openal.html</a>.
</p></dd>
<dt><strong class="strong">Apple</strong></dt>
<dd><p>OpenAL is part of Core Audio, the official Mac OS X Audio interface.
See <a class="url" href="http://developer.apple.com/technologies/mac/audio-and-video.html">http://developer.apple.com/technologies/mac/audio-and-video.html</a>
</p></dd>
</dl>

<p>This device allows one to capture from an audio input device handled
through OpenAL.
</p>
<p>You need to specify the name of the device to capture in the provided
filename. If the empty string is provided, the device will
automatically select the default device. You can get the list of the
supported devices by using the option <var class="var">list_devices</var>.
</p>
<a name="Options-13"></a>
<h4 class="subsection">3.14.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-13" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels in the captured audio. Only the values
<samp class="option">1</samp> (monaural) and <samp class="option">2</samp> (stereo) are currently supported.
Defaults to <samp class="option">2</samp>.
</p>
</dd>
<dt><samp class="option">sample_size</samp></dt>
<dd><p>Set the sample size (in bits) of the captured audio. Only the values
<samp class="option">8</samp> and <samp class="option">16</samp> are currently supported. Defaults to
<samp class="option">16</samp>.
</p>
</dd>
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate (in Hz) of the captured audio.
Defaults to <samp class="option">44.1k</samp>.
</p>
</dd>
<dt><samp class="option">list_devices</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of devices and exit.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
</dl>

<a name="Examples-6"></a>
<h4 class="subsection">3.14.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-6" aria-hidden="true">TOC</a></span></h4>

<p>Print the list of OpenAL supported devices and exit:
</p><div class="example">
<pre class="example-preformatted">$ ffmpeg -list_devices true -f openal -i dummy out.ogg
</pre></div>

<p>Capture from the OpenAL device <samp class="file">DR-BT101 via PulseAudio</samp>:
</p><div class="example">
<pre class="example-preformatted">$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out.ogg
</pre></div>

<p>Capture from the default device (note the empty string &rdquo; as filename):
</p><div class="example">
<pre class="example-preformatted">$ ffmpeg -f openal -i '' out.ogg
</pre></div>

<p>Capture from two devices simultaneously, writing to two different files,
within the same <code class="command">ffmpeg</code> command:
</p><div class="example">
<pre class="example-preformatted">$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out1.ogg -f openal -i 'ALSA Default' out2.ogg
</pre></div>
<p>Note: not all OpenAL implementations support multiple simultaneous capture -
try the latest OpenAL Soft if the above does not work.
</p>
<a name="oss"></a>
<h3 class="section">3.15 oss<span class="pull-right"><a class="anchor hidden-xs" href="#oss" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-oss" aria-hidden="true">TOC</a></span></h3>

<p>Open Sound System input device.
</p>
<p>The filename to provide to the input device is the device node
representing the OSS input device, and is usually set to
<samp class="file">/dev/dsp</samp>.
</p>
<p>For example to grab from <samp class="file">/dev/dsp</samp> using <code class="command">ffmpeg</code> use the
command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f oss -i /dev/dsp /tmp/oss.wav
</pre></div>

<p>For more information about OSS see:
<a class="url" href="http://manuals.opensound.com/usersguide/dsp.html">http://manuals.opensound.com/usersguide/dsp.html</a>
</p>
<a name="Options-14"></a>
<h4 class="subsection">3.15.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-14" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="pulse"></a>
<h3 class="section">3.16 pulse<span class="pull-right"><a class="anchor hidden-xs" href="#pulse" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pulse" aria-hidden="true">TOC</a></span></h3>

<p>PulseAudio input device.
</p>
<p>To enable this output device you need to configure FFmpeg with <code class="code">--enable-libpulse</code>.
</p>
<p>The filename to provide to the input device is a source device or the
string &quot;default&quot;
</p>
<p>To list the PulseAudio source devices and their properties you can invoke
the command <code class="command">pactl list sources</code>.
</p>
<p>More information about PulseAudio can be found on <a class="url" href="http://www.pulseaudio.org">http://www.pulseaudio.org</a>.
</p>
<a name="Options-15"></a>
<h4 class="subsection">3.16.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-15" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">server</samp></dt>
<dd><p>Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.
</p>
</dd>
<dt><samp class="option">name</samp></dt>
<dd><p>Specify the application name PulseAudio will use when showing active clients,
by default it is the <code class="code">LIBAVFORMAT_IDENT</code> string.
</p>
</dd>
<dt><samp class="option">stream_name</samp></dt>
<dd><p>Specify the stream name PulseAudio will use when showing active streams,
by default it is &quot;record&quot;.
</p>
</dd>
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Specify the samplerate in Hz, by default 48kHz is used.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Specify the channels in use, by default 2 (stereo) is set.
</p>
</dd>
<dt><samp class="option">frame_size</samp></dt>
<dd><p>This option does nothing and is deprecated.
</p>
</dd>
<dt><samp class="option">fragment_size</samp></dt>
<dd><p>Specify the size in bytes of the minimal buffering fragment in PulseAudio, it
will affect the audio latency. By default it is set to 50 ms amount of data.
</p>
</dd>
<dt><samp class="option">wallclock</samp></dt>
<dd><p>Set the initial PTS using the current time. Default is 1.
</p>
</dd>
</dl>

<a name="Examples-7"></a>
<h4 class="subsection">3.16.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-7" aria-hidden="true">TOC</a></span></h4>
<p>Record a stream from default device:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f pulse -i default /tmp/pulse.wav
</pre></div>

<a name="sndio"></a>
<h3 class="section">3.17 sndio<span class="pull-right"><a class="anchor hidden-xs" href="#sndio" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sndio" aria-hidden="true">TOC</a></span></h3>

<p>sndio input device.
</p>
<p>To enable this input device during configuration you need libsndio
installed on your system.
</p>
<p>The filename to provide to the input device is the device node
representing the sndio input device, and is usually set to
<samp class="file">/dev/audio0</samp>.
</p>
<p>For example to grab from <samp class="file">/dev/audio0</samp> using <code class="command">ffmpeg</code> use the
command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f sndio -i /dev/audio0 /tmp/oss.wav
</pre></div>

<a name="Options-16"></a>
<h4 class="subsection">3.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-16" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="video4linux2_002c-v4l2"></a>
<h3 class="section">3.18 video4linux2, v4l2<span class="pull-right"><a class="anchor hidden-xs" href="#video4linux2_002c-v4l2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-video4linux2_002c-v4l2" aria-hidden="true">TOC</a></span></h3>

<p>Video4Linux2 input video device.
</p>
<p>&quot;v4l2&quot; can be used as alias for &quot;video4linux2&quot;.
</p>
<p>If FFmpeg is built with v4l-utils support (by using the
<code class="code">--enable-libv4l2</code> configure option), it is possible to use it with the
<code class="code">-use_libv4l2</code> input device option.
</p>
<p>The name of the device to grab is a file device node, usually Linux
systems tend to automatically create such nodes when the device
(e.g. an USB webcam) is plugged into the system, and has a name of the
kind <samp class="file">/dev/video<var class="var">N</var></samp>, where <var class="var">N</var> is a number associated to
the device.
</p>
<p>Video4Linux2 devices usually support a limited set of
<var class="var">width</var>x<var class="var">height</var> sizes and frame rates. You can check which are
supported using <code class="command">-list_formats all</code> for Video4Linux2 devices.
Some devices, like TV cards, support one or more standards. It is possible
to list all the supported standards using <code class="command">-list_standards all</code>.
</p>
<p>The time base for the timestamps is 1 microsecond. Depending on the kernel
version and configuration, the timestamps may be derived from the real time
clock (origin at the Unix Epoch) or the monotonic clock (origin usually at
boot time, unaffected by NTP or manual changes to the clock). The
<samp class="option">-timestamps abs</samp> or <samp class="option">-ts abs</samp> option can be used to force
conversion into the real time clock.
</p>
<p>Some usage examples of the video4linux2 device with <code class="command">ffmpeg</code>
and <code class="command">ffplay</code>:
</p><ul class="itemize mark-bullet">
<li>List supported formats for a video4linux2 device:
<div class="example">
<pre class="example-preformatted">ffplay -f video4linux2 -list_formats all /dev/video0
</pre></div>

</li><li>Grab and show the input of a video4linux2 device:
<div class="example">
<pre class="example-preformatted">ffplay -f video4linux2 -framerate 30 -video_size hd720 /dev/video0
</pre></div>

</li><li>Grab and record the input of a video4linux2 device, leave the
frame rate and size as previously set:
<div class="example">
<pre class="example-preformatted">ffmpeg -f video4linux2 -input_format mjpeg -i /dev/video0 out.mpeg
</pre></div>
</li></ul>

<p>For more information about Video4Linux, check <a class="url" href="http://linuxtv.org/">http://linuxtv.org/</a>.
</p>
<a name="Options-17"></a>
<h4 class="subsection">3.18.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-17" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">standard</samp></dt>
<dd><p>Set the standard. Must be the name of a supported standard. To get a
list of the supported standards, use the <samp class="option">list_standards</samp>
option.
</p>
</dd>
<dt><samp class="option">channel</samp></dt>
<dd><p>Set the input channel number. Default to -1, which means using the
previously selected channel.
</p>
</dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video frame size. The argument must be a string in the form
<var class="var">WIDTH</var>x<var class="var">HEIGHT</var> or a valid size abbreviation.
</p>
</dd>
<dt><samp class="option">pixel_format</samp></dt>
<dd><p>Select the pixel format (only valid for raw video input).
</p>
</dd>
<dt><samp class="option">input_format</samp></dt>
<dd><p>Set the preferred pixel format (for raw video) or a codec name.
This option allows one to select the input format, when several are
available.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the preferred video frame rate.
</p>
</dd>
<dt><samp class="option">list_formats</samp></dt>
<dd><p>List available formats (supported pixel formats, codecs, and frame
sizes) and exit.
</p>
<p>Available values are:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">all</samp>&rsquo;</dt>
<dd><p>Show all available (compressed and non-compressed) formats.
</p>
</dd>
<dt>&lsquo;<samp class="samp">raw</samp>&rsquo;</dt>
<dd><p>Show only raw video (non-compressed) formats.
</p>
</dd>
<dt>&lsquo;<samp class="samp">compressed</samp>&rsquo;</dt>
<dd><p>Show only compressed formats.
</p></dd>
</dl>

</dd>
<dt><samp class="option">list_standards</samp></dt>
<dd><p>List supported standards and exit.
</p>
<p>Available values are:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">all</samp>&rsquo;</dt>
<dd><p>Show all supported standards.
</p></dd>
</dl>

</dd>
<dt><samp class="option">timestamps, ts</samp></dt>
<dd><p>Set type of timestamps for grabbed frames.
</p>
<p>Available values are:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dd><p>Use timestamps from the kernel.
</p>
</dd>
<dt>&lsquo;<samp class="samp">abs</samp>&rsquo;</dt>
<dd><p>Use absolute timestamps (wall clock).
</p>
</dd>
<dt>&lsquo;<samp class="samp">mono2abs</samp>&rsquo;</dt>
<dd><p>Force conversion from monotonic to absolute timestamps.
</p></dd>
</dl>

<p>Default value is <code class="code">default</code>.
</p>
</dd>
<dt><samp class="option">use_libv4l2</samp></dt>
<dd><p>Use libv4l2 (v4l-utils) conversion functions. Default is 0.
</p>
</dd>
</dl>

<a name="vfwcap"></a>
<h3 class="section">3.19 vfwcap<span class="pull-right"><a class="anchor hidden-xs" href="#vfwcap" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vfwcap" aria-hidden="true">TOC</a></span></h3>

<p>VfW (Video for Windows) capture input device.
</p>
<p>The filename passed as input is the capture driver number, ranging from
0 to 9. You may use &quot;list&quot; as filename to print a list of drivers. Any
other filename will be interpreted as device number 0.
</p>
<a name="Options-18"></a>
<h4 class="subsection">3.19.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-18" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-18" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video frame size.
</p>
</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code class="code">ntsc</code>,
corresponding to a frame rate of <code class="code">30000/1001</code>.
</p>
</dd>
</dl>

<a name="x11grab"></a>
<h3 class="section">3.20 x11grab<span class="pull-right"><a class="anchor hidden-xs" href="#x11grab" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-x11grab" aria-hidden="true">TOC</a></span></h3>

<p>X11 video input device.
</p>
<p>To enable this input device during configuration you need libxcb
installed on your system. It will be automatically detected during
configuration.
</p>
<p>This device allows one to capture a region of an X11 display.
</p>
<p>The filename passed as input has the syntax:
</p><div class="example">
<pre class="example-preformatted">[<var class="var">hostname</var>]:<var class="var">display_number</var>.<var class="var">screen_number</var>[+<var class="var">x_offset</var>,<var class="var">y_offset</var>]
</pre></div>

<p><var class="var">hostname</var>:<var class="var">display_number</var>.<var class="var">screen_number</var> specifies the
X11 display name of the screen to grab from. <var class="var">hostname</var> can be
omitted, and defaults to &quot;localhost&quot;. The environment variable
<code class="env">DISPLAY</code> contains the default display name.
</p>
<p><var class="var">x_offset</var> and <var class="var">y_offset</var> specify the offsets of the grabbed
area with respect to the top-left border of the X11 screen. They
default to 0.
</p>
<p>Check the X11 documentation (e.g. <code class="command">man X</code>) for more detailed
information.
</p>
<p>Use the <code class="command">xdpyinfo</code> program for getting basic information about
the properties of your X11 display (e.g. grep for &quot;name&quot; or
&quot;dimensions&quot;).
</p>
<p>For example to grab from <samp class="file">:0.0</samp> using <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

<p>Grab at position <code class="code">10,20</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0+10,20 out.mpg
</pre></div>

<a name="Options-19"></a>
<h4 class="subsection">3.20.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-19" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-19" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">select_region</samp></dt>
<dd><p>Specify whether to select the grabbing area graphically using the pointer.
A value of <code class="code">1</code> prompts the user to select the grabbing area graphically
by clicking and dragging. A single click with no dragging will select the
whole screen. A region with zero width or height will also select the whole
screen. This option overwrites the <var class="var">video_size</var>, <var class="var">grab_x</var>, and
<var class="var">grab_y</var> options. Default value is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">draw_mouse</samp></dt>
<dd><p>Specify whether to draw the mouse pointer. A value of <code class="code">0</code> specifies
not to draw the pointer. Default value is <code class="code">1</code>.
</p>
</dd>
<dt><samp class="option">follow_mouse</samp></dt>
<dd><p>Make the grabbed area follow the mouse. The argument can be
<code class="code">centered</code> or a number of pixels <var class="var">PIXELS</var>.
</p>
<p>When it is specified with &quot;centered&quot;, the grabbing region follows the mouse
pointer and keeps the pointer at the center of region; otherwise, the region
follows only when the mouse pointer reaches within <var class="var">PIXELS</var> (greater than
zero) to the edge of region.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -follow_mouse centered -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

<p>To follow only when the mouse pointer reaches within 100 pixels to edge:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -follow_mouse 100 -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

</dd>
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code class="code">ntsc</code>,
corresponding to a frame rate of <code class="code">30000/1001</code>.
</p>
</dd>
<dt><samp class="option">show_region</samp></dt>
<dd><p>Show grabbed region on screen.
</p>
<p>If <var class="var">show_region</var> is specified with <code class="code">1</code>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.
</p>
</dd>
<dt><samp class="option">region_border</samp></dt>
<dd><p>Set the region border thickness if <samp class="option">-show_region 1</samp> is used.
Range is 1 to 128 and default is 3 (XCB-based x11grab only).
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -show_region 1 -framerate 25 -video_size cif -i :0.0+10,20 out.mpg
</pre></div>

<p>With <var class="var">follow_mouse</var>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -follow_mouse centered -show_region 1 -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

</dd>
<dt><samp class="option">window_id</samp></dt>
<dd><p>Grab this window, instead of the whole screen. Default value is 0, which maps to
the whole screen (root window).
</p>
<p>The id of a window can be found using the <code class="command">xwininfo</code> program, possibly with options -tree and
-root.
</p>
<p>If the window is later enlarged, the new area is not recorded. Video ends when
the window is closed, unmapped (i.e., iconified) or shrunk beyond the video
size (which defaults to the initial window size).
</p>
<p>This option disables options <samp class="option">follow_mouse</samp> and <samp class="option">select_region</samp>.
</p>
</dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video frame size. Default is the full desktop or window.
</p>
</dd>
<dt><samp class="option">grab_x</samp></dt>
<dt><samp class="option">grab_y</samp></dt>
<dd><p>Set the grabbing region coordinates. They are expressed as offset from
the top left corner of the X11 window and correspond to the
<var class="var">x_offset</var> and <var class="var">y_offset</var> parameters in the device name. The
default value for both options is 0.
</p></dd>
</dl>

<a name="Output-Devices"></a>
<h2 class="chapter">4 Output Devices<span class="pull-right"><a class="anchor hidden-xs" href="#Output-Devices" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Output-Devices" aria-hidden="true">TOC</a></span></h2>

<p>Output devices are configured elements in FFmpeg that can write
multimedia data to an output device attached to your system.
</p>
<p>When you configure your FFmpeg build, all the supported output devices
are enabled by default. You can list all available ones using the
configure option &quot;&ndash;list-outdevs&quot;.
</p>
<p>You can disable all the output devices using the configure option
&quot;&ndash;disable-outdevs&quot;, and selectively enable an output device using the
option &quot;&ndash;enable-outdev=<var class="var">OUTDEV</var>&quot;, or you can disable a particular
input device using the option &quot;&ndash;disable-outdev=<var class="var">OUTDEV</var>&quot;.
</p>
<p>The option &quot;-devices&quot; of the ff* tools will display the list of
enabled output devices.
</p>
<p>A description of the currently available output devices follows.
</p>
<a name="alsa-1"></a>
<h3 class="section">4.1 alsa<span class="pull-right"><a class="anchor hidden-xs" href="#alsa-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alsa-1" aria-hidden="true">TOC</a></span></h3>

<p>ALSA (Advanced Linux Sound Architecture) output device.
</p>
<a name="Examples-8"></a>
<h4 class="subsection">4.1.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-8" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Play a file on default ALSA device:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f alsa default
</pre></div>

</li><li>Play a file on soundcard 1, audio device 7:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f alsa hw:1,7
</pre></div>
</li></ul>

<a name="AudioToolbox"></a>
<h3 class="section">4.2 AudioToolbox<span class="pull-right"><a class="anchor hidden-xs" href="#AudioToolbox" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-AudioToolbox" aria-hidden="true">TOC</a></span></h3>

<p>AudioToolbox output device.
</p>
<p>Allows native output to CoreAudio devices on OSX.
</p>
<p>The output filename can be empty (or <code class="code">-</code>) to refer to the default system output device or a number that refers to the device index as shown using: <code class="code">-list_devices true</code>.
</p>
<p>Alternatively, the audio input device can be chosen by index using the
<samp class="option">
    -audio_device_index &lt;INDEX&gt;
</samp>
, overriding any device name or index given in the input filename.
</p>
<p>All available devices can be enumerated by using <samp class="option">-list_devices true</samp>, listing
all device names, UIDs and corresponding indices.
</p>
<a name="Options-20"></a>
<h4 class="subsection">4.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-20" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-20" aria-hidden="true">TOC</a></span></h4>

<p>AudioToolbox supports the following options:
</p>
<dl class="table">
<dt><samp class="option">-audio_device_index &lt;INDEX&gt;</samp></dt>
<dd><p>Specify the audio device by its index. Overrides anything given in the output filename.
</p>
</dd>
</dl>

<a name="Examples-9"></a>
<h4 class="subsection">4.2.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-9" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Print the list of supported devices and output a sine wave to the default device:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f lavfi -i sine=r=44100 -f audiotoolbox -list_devices true -
</pre></div>

</li><li>Output a sine wave to the device with the index 2, overriding any output filename:
<div class="example">
<pre class="example-preformatted">$ ffmpeg -f lavfi -i sine=r=44100 -f audiotoolbox -audio_device_index 2 -
</pre></div>

</li></ul>

<a name="caca"></a>
<h3 class="section">4.3 caca<span class="pull-right"><a class="anchor hidden-xs" href="#caca" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-caca" aria-hidden="true">TOC</a></span></h3>

<p>CACA output device.
</p>
<p>This output device allows one to show a video stream in CACA window.
Only one CACA window is allowed per application, so you can
have only one instance of this output device in an application.
</p>
<p>To enable this output device you need to configure FFmpeg with
<code class="code">--enable-libcaca</code>.
libcaca is a graphics library that outputs text instead of pixels.
</p>
<p>For more information about libcaca, check:
<a class="url" href="http://caca.zoy.org/wiki/libcaca">http://caca.zoy.org/wiki/libcaca</a>
</p>
<a name="Options-21"></a>
<h4 class="subsection">4.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-21" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-21" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">window_title</samp></dt>
<dd><p>Set the CACA window title, if not specified default to the filename
specified for the output device.
</p>
</dd>
<dt><samp class="option">window_size</samp></dt>
<dd><p>Set the CACA window size, can be a string of the form
<var class="var">width</var>x<var class="var">height</var> or a video size abbreviation.
If not specified it defaults to the size of the input video.
</p>
</dd>
<dt><samp class="option">driver</samp></dt>
<dd><p>Set display driver.
</p>
</dd>
<dt><samp class="option">algorithm</samp></dt>
<dd><p>Set dithering algorithm. Dithering is necessary
because the picture being rendered has usually far more colours than
the available palette.
The accepted values are listed with <code class="code">-list_dither algorithms</code>.
</p>
</dd>
<dt><samp class="option">antialias</samp></dt>
<dd><p>Set antialias method. Antialiasing smoothens the rendered
image and avoids the commonly seen staircase effect.
The accepted values are listed with <code class="code">-list_dither antialiases</code>.
</p>
</dd>
<dt><samp class="option">charset</samp></dt>
<dd><p>Set which characters are going to be used when rendering text.
The accepted values are listed with <code class="code">-list_dither charsets</code>.
</p>
</dd>
<dt><samp class="option">color</samp></dt>
<dd><p>Set color to be used when rendering text.
The accepted values are listed with <code class="code">-list_dither colors</code>.
</p>
</dd>
<dt><samp class="option">list_drivers</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of available drivers and exit.
</p>
</dd>
<dt><samp class="option">list_dither</samp></dt>
<dd><p>List available dither options related to the argument.
The argument must be one of <code class="code">algorithms</code>, <code class="code">antialiases</code>,
<code class="code">charsets</code>, <code class="code">colors</code>.
</p></dd>
</dl>

<a name="Examples-10"></a>
<h4 class="subsection">4.3.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-10" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>The following command shows the <code class="command">ffmpeg</code> output is an
CACA window, forcing its size to 80x25:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -c:v rawvideo -pix_fmt rgb24 -window_size 80x25 -f caca -
</pre></div>

</li><li>Show the list of available drivers and exit:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_drivers true -
</pre></div>

</li><li>Show the list of available dither colors and exit:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_dither colors -
</pre></div>
</li></ul>

<a name="decklink-1"></a>
<h3 class="section">4.4 decklink<span class="pull-right"><a class="anchor hidden-xs" href="#decklink-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-decklink-1" aria-hidden="true">TOC</a></span></h3>

<p>The decklink output device provides playback capabilities for Blackmagic
DeckLink devices.
</p>
<p>To enable this output device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate <code class="code">--extra-cflags</code>
and <code class="code">--extra-ldflags</code>.
On Windows, you need to run the IDL files through <code class="command">widl</code>.
</p>
<p>DeckLink is very picky about the formats it supports. Pixel format is always
uyvy422, framerate, field order and video size must be determined for your
device with <code class="command">-list_formats 1</code>. Audio sample rate is always 48 kHz.
</p>
<a name="Options-22"></a>
<h4 class="subsection">4.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-22" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-22" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">list_devices</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of devices and exit.
Defaults to <samp class="option">false</samp>. This option is deprecated, please use the
<code class="code">-sinks</code> option of ffmpeg to list the available output devices.
</p>
</dd>
<dt><samp class="option">list_formats</samp></dt>
<dd><p>If set to <samp class="option">true</samp>, print a list of supported formats and exit.
Defaults to <samp class="option">false</samp>.
</p>
</dd>
<dt><samp class="option">preroll</samp></dt>
<dd><p>Amount of time to preroll video in seconds.
Defaults to <samp class="option">0.5</samp>.
</p>
</dd>
<dt><samp class="option">duplex_mode</samp></dt>
<dd><p>Sets the decklink device duplex/profile mode. Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">half</samp>&rsquo;, &lsquo;<samp class="samp">full</samp>&rsquo;,
&lsquo;<samp class="samp">one_sub_device_full</samp>&rsquo;, &lsquo;<samp class="samp">one_sub_device_half</samp>&rsquo;, &lsquo;<samp class="samp">two_sub_device_full</samp>&rsquo;,
&lsquo;<samp class="samp">four_sub_device_half</samp>&rsquo;
Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
<p>Note: DeckLink SDK 11.0 have replaced the duplex property by a profile property.
For the DeckLink Duo 2 and DeckLink Quad 2, a profile is shared between any 2
sub-devices that utilize the same connectors. For the DeckLink 8K Pro, a profile
is shared between all 4 sub-devices. So DeckLink 8K Pro support four profiles.
</p>
<p>Valid profile modes for DeckLink 8K Pro(with DeckLink SDK &gt;= 11.0):
&lsquo;<samp class="samp">one_sub_device_full</samp>&rsquo;, &lsquo;<samp class="samp">one_sub_device_half</samp>&rsquo;, &lsquo;<samp class="samp">two_sub_device_full</samp>&rsquo;,
&lsquo;<samp class="samp">four_sub_device_half</samp>&rsquo;
</p>
<p>Valid profile modes for DeckLink Quad 2 and DeckLink Duo 2:
&lsquo;<samp class="samp">half</samp>&rsquo;, &lsquo;<samp class="samp">full</samp>&rsquo;
</p>
</dd>
<dt><samp class="option">timing_offset</samp></dt>
<dd><p>Sets the genlock timing pixel offset on the used output.
Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">link</samp></dt>
<dd><p>Sets the SDI video link configuration on the used output. Must be
&lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">single</samp>&rsquo; link SDI, &lsquo;<samp class="samp">dual</samp>&rsquo; link SDI or &lsquo;<samp class="samp">quad</samp>&rsquo; link
SDI.
Defaults to &lsquo;<samp class="samp">unset</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">sqd</samp></dt>
<dd><p>Enable Square Division Quad Split mode for Quad-link SDI output.
Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">true</samp>&rsquo; or &lsquo;<samp class="samp">false</samp>&rsquo;.
Defaults to <samp class="option">unset</samp>.
</p>
</dd>
<dt><samp class="option">level_a</samp></dt>
<dd><p>Enable SMPTE Level A mode on the used output.
Must be &lsquo;<samp class="samp">unset</samp>&rsquo;, &lsquo;<samp class="samp">true</samp>&rsquo; or &lsquo;<samp class="samp">false</samp>&rsquo;.
Defaults to <samp class="option">unset</samp>.
</p>
</dd>
<dt><samp class="option">vanc_queue_size</samp></dt>
<dd><p>Sets maximum output buffer size in bytes for VANC data. If the buffering reaches this value,
outgoing VANC data will be dropped.
Defaults to &lsquo;<samp class="samp">1048576</samp>&rsquo;.
</p>
</dd>
</dl>

<a name="Examples-11"></a>
<h4 class="subsection">4.4.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-11" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>List output devices:
<div class="example">
<pre class="example-preformatted">ffmpeg -sinks decklink
</pre></div>

</li><li>List supported formats:
<div class="example">
<pre class="example-preformatted">ffmpeg -i test.avi -f decklink -list_formats 1 'DeckLink Mini Monitor'
</pre></div>

</li><li>Play video clip:
<div class="example">
<pre class="example-preformatted">ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 'DeckLink Mini Monitor'
</pre></div>

</li><li>Play video clip with non-standard framerate or video size:
<div class="example">
<pre class="example-preformatted">ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 -s 720x486 -r 24000/1001 'DeckLink Mini Monitor'
</pre></div>

</li></ul>

<a name="fbdev-1"></a>
<h3 class="section">4.5 fbdev<span class="pull-right"><a class="anchor hidden-xs" href="#fbdev-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fbdev-1" aria-hidden="true">TOC</a></span></h3>

<p>Linux framebuffer output device.
</p>
<p>The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
<samp class="file">/dev/fb0</samp>.
</p>
<p>For more detailed information read the file
<samp class="file">Documentation/fb/framebuffer.txt</samp> included in the Linux source tree.
</p>
<a name="Options-23"></a>
<h4 class="subsection">4.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-23" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-23" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">xoffset</samp></dt>
<dt><samp class="option">yoffset</samp></dt>
<dd><p>Set x/y coordinate of top left corner. Default is 0.
</p></dd>
</dl>

<a name="Examples-12"></a>
<h4 class="subsection">4.5.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-12" aria-hidden="true">TOC</a></span></h4>
<p>Play a file on framebuffer device <samp class="file">/dev/fb0</samp>.
Required pixel format depends on current framebuffer settings.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i INPUT -c:v rawvideo -pix_fmt bgra -f fbdev /dev/fb0
</pre></div>

<p>See also <a class="url" href="http://linux-fbdev.sourceforge.net/">http://linux-fbdev.sourceforge.net/</a>, and fbset(1).
</p>
<a name="oss-1"></a>
<h3 class="section">4.6 oss<span class="pull-right"><a class="anchor hidden-xs" href="#oss-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-oss-1" aria-hidden="true">TOC</a></span></h3>

<p>OSS (Open Sound System) output device.
</p>
<a name="pulse-1"></a>
<h3 class="section">4.7 pulse<span class="pull-right"><a class="anchor hidden-xs" href="#pulse-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pulse-1" aria-hidden="true">TOC</a></span></h3>

<p>PulseAudio output device.
</p>
<p>To enable this output device you need to configure FFmpeg with <code class="code">--enable-libpulse</code>.
</p>
<p>More information about PulseAudio can be found on <a class="url" href="http://www.pulseaudio.org">http://www.pulseaudio.org</a>
</p>
<a name="Options-24"></a>
<h4 class="subsection">4.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-24" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-24" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">server</samp></dt>
<dd><p>Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.
</p>
</dd>
<dt><samp class="option">name</samp></dt>
<dd><p>Specify the application name PulseAudio will use when showing active clients,
by default it is the <code class="code">LIBAVFORMAT_IDENT</code> string.
</p>
</dd>
<dt><samp class="option">stream_name</samp></dt>
<dd><p>Specify the stream name PulseAudio will use when showing active streams,
by default it is set to the specified output name.
</p>
</dd>
<dt><samp class="option">device</samp></dt>
<dd><p>Specify the device to use. Default device is used when not provided.
List of output devices can be obtained with command <code class="command">pactl list sinks</code>.
</p>
</dd>
<dt><samp class="option">buffer_size</samp></dt>
<dt><samp class="option">buffer_duration</samp></dt>
<dd><p>Control the size and duration of the PulseAudio buffer. A small buffer
gives more control, but requires more frequent updates.
</p>
<p><samp class="option">buffer_size</samp> specifies size in bytes while
<samp class="option">buffer_duration</samp> specifies duration in milliseconds.
</p>
<p>When both options are provided then the highest value is used
(duration is recalculated to bytes using stream parameters). If they
are set to 0 (which is default), the device will use the default
PulseAudio duration value. By default PulseAudio set buffer duration
to around 2 seconds.
</p>
</dd>
<dt><samp class="option">prebuf</samp></dt>
<dd><p>Specify pre-buffering size in bytes. The server does not start with
playback before at least <samp class="option">prebuf</samp> bytes are available in the
buffer. By default this option is initialized to the same value as
<samp class="option">buffer_size</samp> or <samp class="option">buffer_duration</samp> (whichever is bigger).
</p>
</dd>
<dt><samp class="option">minreq</samp></dt>
<dd><p>Specify minimum request size in bytes. The server does not request less
than <samp class="option">minreq</samp> bytes from the client, instead waits until the buffer
is free enough to request more bytes at once. It is recommended to not set
this option, which will initialize this to a value that is deemed sensible
by the server.
</p>
</dd>
</dl>

<a name="Examples-13"></a>
<h4 class="subsection">4.7.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-13" aria-hidden="true">TOC</a></span></h4>
<p>Play a file on default device on default server:
</p><div class="example">
<pre class="example-preformatted">ffmpeg  -i INPUT -f pulse &quot;stream name&quot;
</pre></div>

<a name="sndio-1"></a>
<h3 class="section">4.8 sndio<span class="pull-right"><a class="anchor hidden-xs" href="#sndio-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sndio-1" aria-hidden="true">TOC</a></span></h3>

<p>sndio audio output device.
</p>
<a name="v4l2"></a>
<h3 class="section">4.9 v4l2<span class="pull-right"><a class="anchor hidden-xs" href="#v4l2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-v4l2" aria-hidden="true">TOC</a></span></h3>

<p>Video4Linux2 output device.
</p>
<a name="xv"></a>
<h3 class="section">4.10 xv<span class="pull-right"><a class="anchor hidden-xs" href="#xv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-xv" aria-hidden="true">TOC</a></span></h3>

<p>XV (XVideo) output device.
</p>
<p>This output device allows one to show a video stream in a X Window System
window.
</p>
<a name="Options-25"></a>
<h4 class="subsection">4.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-25" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-25" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">display_name</samp></dt>
<dd><p>Specify the hardware display name, which determines the display and
communications domain to be used.
</p>
<p>The display name or DISPLAY environment variable can be a string in
the format <var class="var">hostname</var>[:<var class="var">number</var>[.<var class="var">screen_number</var>]].
</p>
<p><var class="var">hostname</var> specifies the name of the host machine on which the
display is physically attached. <var class="var">number</var> specifies the number of
the display server on that host machine. <var class="var">screen_number</var> specifies
the screen to be used on that server.
</p>
<p>If unspecified, it defaults to the value of the DISPLAY environment
variable.
</p>
<p>For example, <code class="code">dual-headed:0.1</code> would specify screen 1 of display
0 on the machine named &ldquo;dual-headed&rdquo;.
</p>
<p>Check the X11 specification for more detailed information about the
display name format.
</p>
</dd>
<dt><samp class="option">window_id</samp></dt>
<dd><p>When set to non-zero value then device doesn&rsquo;t create new window,
but uses existing one with provided <var class="var">window_id</var>. By default
this options is set to zero and device creates its own window.
</p>
</dd>
<dt><samp class="option">window_size</samp></dt>
<dd><p>Set the created window size, can be a string of the form
<var class="var">width</var>x<var class="var">height</var> or a video size abbreviation. If not
specified it defaults to the size of the input video.
Ignored when <var class="var">window_id</var> is set.
</p>
</dd>
<dt><samp class="option">window_x</samp></dt>
<dt><samp class="option">window_y</samp></dt>
<dd><p>Set the X and Y window offsets for the created window. They are both
set to 0 by default. The values may be ignored by the window manager.
Ignored when <var class="var">window_id</var> is set.
</p>
</dd>
<dt><samp class="option">window_title</samp></dt>
<dd><p>Set the window title, if not specified default to the filename
specified for the output device. Ignored when <var class="var">window_id</var> is set.
</p></dd>
</dl>

<p>For more information about XVideo see <a class="url" href="http://www.x.org/">http://www.x.org/</a>.
</p>
<a name="Examples-14"></a>
<h4 class="subsection">4.10.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-14" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Decode, display and encode video input with <code class="command">ffmpeg</code> at the
same time:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT OUTPUT -f xv display
</pre></div>

</li><li>Decode and display the input video to multiple X11 windows:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f xv normal -vf negate -f xv negated
</pre></div>
</li></ul>


<a name="See-Also"></a>
<h2 class="chapter">5 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libavdevice.html">libavdevice</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">6 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
