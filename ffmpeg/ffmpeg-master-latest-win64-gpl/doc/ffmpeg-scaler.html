<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Scaler Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Scaler Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Scaler-Options" href="#Scaler-Options">2 Scaler Options</a></li>
  <li><a id="toc-See-Also" href="#See-Also">3 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">4 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg rescaler provides a high-level interface to the libswscale
library image conversion utilities. In particular it allows one to perform
image rescaling and pixel format conversion.
</p>

<a class="anchor" id="scaler_005foptions"></a><a name="Scaler-Options"></a>
<h2 class="chapter">2 Scaler Options<span class="pull-right"><a class="anchor hidden-xs" href="#Scaler-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Scaler-Options" aria-hidden="true">TOC</a></span></h2>

<p>The video scaler supports the following named options.
</p>
<p>Options may be set by specifying -<var class="var">option</var> <var class="var">value</var> in the
FFmpeg tools, with a few API-only exceptions noted below.
For programmatic use, they can be set explicitly in the
<code class="code">SwsContext</code> options or through the <samp class="file">libavutil/opt.h</samp> API.
</p>
<dl class="table">
<dd>
<a class="anchor" id="sws_005fflags"></a></dd>
<dt><samp class="option">sws_flags</samp></dt>
<dd><p>Set the scaler flags. This is also used to set the scaling
algorithm. Only a single algorithm should be selected. Default
value is &lsquo;<samp class="samp">bicubic</samp>&rsquo;.
</p>
<p>It accepts the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">fast_bilinear</samp>&rsquo;</dt>
<dd><p>Select fast bilinear scaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bilinear</samp>&rsquo;</dt>
<dd><p>Select bilinear scaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bicubic</samp>&rsquo;</dt>
<dd><p>Select bicubic scaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">experimental</samp>&rsquo;</dt>
<dd><p>Select experimental scaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">neighbor</samp>&rsquo;</dt>
<dd><p>Select nearest neighbor rescaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">area</samp>&rsquo;</dt>
<dd><p>Select averaging area rescaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bicublin</samp>&rsquo;</dt>
<dd><p>Select bicubic scaling algorithm for the luma component, bilinear for
chroma components.
</p>
</dd>
<dt>&lsquo;<samp class="samp">gauss</samp>&rsquo;</dt>
<dd><p>Select Gaussian rescaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">sinc</samp>&rsquo;</dt>
<dd><p>Select sinc rescaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">lanczos</samp>&rsquo;</dt>
<dd><p>Select Lanczos rescaling algorithm. The default width (alpha) is 3 and can be
changed by setting <code class="code">param0</code>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">spline</samp>&rsquo;</dt>
<dd><p>Select natural bicubic spline rescaling algorithm.
</p>
</dd>
<dt>&lsquo;<samp class="samp">print_info</samp>&rsquo;</dt>
<dd><p>Enable printing/debug logging.
</p>
</dd>
<dt>&lsquo;<samp class="samp">accurate_rnd</samp>&rsquo;</dt>
<dd><p>Enable accurate rounding.
</p>
</dd>
<dt>&lsquo;<samp class="samp">full_chroma_int</samp>&rsquo;</dt>
<dd><p>Enable full chroma interpolation.
</p>
</dd>
<dt>&lsquo;<samp class="samp">full_chroma_inp</samp>&rsquo;</dt>
<dd><p>Select full chroma input.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bitexact</samp>&rsquo;</dt>
<dd><p>Enable bitexact output.
</p></dd>
</dl>

</dd>
<dt><samp class="option">srcw <var class="var">(API only)</var></samp></dt>
<dd><p>Set source width.
</p>
</dd>
<dt><samp class="option">srch <var class="var">(API only)</var></samp></dt>
<dd><p>Set source height.
</p>
</dd>
<dt><samp class="option">dstw <var class="var">(API only)</var></samp></dt>
<dd><p>Set destination width.
</p>
</dd>
<dt><samp class="option">dsth <var class="var">(API only)</var></samp></dt>
<dd><p>Set destination height.
</p>
</dd>
<dt><samp class="option">src_format <var class="var">(API only)</var></samp></dt>
<dd><p>Set source pixel format (must be expressed as an integer).
</p>
</dd>
<dt><samp class="option">dst_format <var class="var">(API only)</var></samp></dt>
<dd><p>Set destination pixel format (must be expressed as an integer).
</p>
</dd>
<dt><samp class="option">src_range <var class="var">(boolean)</var></samp></dt>
<dd><p>If value is set to <code class="code">1</code>, indicates source is full range. Default value is
<code class="code">0</code>, which indicates source is limited range.
</p>
</dd>
<dt><samp class="option">dst_range <var class="var">(boolean)</var></samp></dt>
<dd><p>If value is set to <code class="code">1</code>, enable full range for destination. Default value
is <code class="code">0</code>, which enables limited range.
</p>
</dd>
<dt><samp class="option">gamma <var class="var">(boolean)</var></samp></dt>
<dd><p>If value is set to <code class="code">1</code>, enable gamma correct scaling. Default value is <code class="code">0</code>.
</p>
<a class="anchor" id="sws_005fparams"></a></dd>
<dt><samp class="option">param0, param1</samp></dt>
<dd><p>Set scaling algorithm parameters. The specified values are specific of
some scaling algorithms and ignored by others. The specified values
are floating point number values.
</p>
</dd>
<dt><samp class="option">sws_dither</samp></dt>
<dd><p>Set the dithering algorithm. Accepts one of the following
values. Default value is &lsquo;<samp class="samp">auto</samp>&rsquo;.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>automatic choice
</p>
</dd>
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dd><p>no dithering
</p>
</dd>
<dt>&lsquo;<samp class="samp">bayer</samp>&rsquo;</dt>
<dd><p>bayer dither
</p>
</dd>
<dt>&lsquo;<samp class="samp">ed</samp>&rsquo;</dt>
<dd><p>error diffusion dither
</p>
</dd>
<dt>&lsquo;<samp class="samp">a_dither</samp>&rsquo;</dt>
<dd><p>arithmetic dither, based using addition
</p>
</dd>
<dt>&lsquo;<samp class="samp">x_dither</samp>&rsquo;</dt>
<dd><p>arithmetic dither, based using xor (more random/less apparent patterning that
a_dither).
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">alphablend</samp></dt>
<dd><p>Set the alpha blending to use when the input has alpha but the output does not.
Default value is &lsquo;<samp class="samp">none</samp>&rsquo;.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">uniform_color</samp>&rsquo;</dt>
<dd><p>Blend onto a uniform background color
</p>
</dd>
<dt>&lsquo;<samp class="samp">checkerboard</samp>&rsquo;</dt>
<dd><p>Blend onto a checkerboard
</p>
</dd>
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dd><p>No blending
</p>
</dd>
</dl>

</dd>
</dl>


<a name="See-Also"></a>
<h2 class="chapter">3 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libswscale.html">libswscale</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">4 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
